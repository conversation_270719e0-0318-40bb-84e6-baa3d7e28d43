<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Define props - receive data from parent component
const props = defineProps({
  serviceRequestData: {
    type: Object,
    default: () => ({
      serviceFormId: null,
      requestId: null,
      servicePackId: null,
      tonerType: null,
      tonerTypeValue: null,
      servicePackDescription: null,
      region: null,
      territory: null,
      coterm: null,
      isDealerAcceptedSr: null,
      paymentMode: null,
      paymentModeValue: null,
      leaseTermInMonth: null,
      msrp: null,
      msrpPercent: null,
      currentUsageInformation: null,
      currentEquipmentInfo: null,
      serviceBusinessCase: null,
      serviceApprovals: [],
      dsdRequestMfpPricing: []
    })
  }
});

// Define types for the service request data
interface ServiceApproval {
  serviceApprovalId: number;
  serviceFormId: number;
  fixedTermInMonth: number;
  itemId: number;
  displayName: string;
  dsdQuantity: number;
  dealerQuantity: number;
  amvUnit: number;
  colourPercentage: number;
  oversizePercentage: number;
  cpcIncludesAccessory: string;
  blackAndWhite: number;
  colour: number;
  iprc: number;
  minimumBaseAmount: number;
  minimumBaseVolume: number;
  userRemark: string;
}

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Format percentage values
const formatPercentage = (value: number | null | undefined): string => {
  if (!value && value !== 0) return 'N/A';
  return `${value}%`;
};

// Format general values
const formatValue = (value: any): string => {
  if (value === null || value === undefined || value === '') return 'N/A';
  return String(value);
};

// Computed properties for service approvals
const serviceApprovals = computed(() => props.serviceRequestData.serviceApprovals || []);
</script>

<template>
  <div>
    <!-- Payment and Lease Details -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Payment and Lease Details</h3>
      <v-table density="compact" class="mb-4">
        <tbody>
          <tr>
            <th class="text-left" width="200">Payment Mode</th>
            <td class="text-left">{{ formatValue(serviceRequestData.paymentMode) }}</td>
          </tr>
          <tr>
            <th class="text-left">Payment Mode Value</th>
            <td class="text-left">{{ formatValue(serviceRequestData.paymentModeValue) }}</td>
          </tr>
          <tr>
            <th class="text-left">Lease Term (Months)</th>
            <td class="text-left">{{ formatValue(serviceRequestData.leaseTermInMonth) }}</td>
          </tr>
          <tr>
            <th class="text-left">MSRP</th>
            <td class="text-left">{{ formatCurrency(serviceRequestData.msrp) }}</td>
          </tr>
          <tr>
            <th class="text-left">MSRP Percent</th>
            <td class="text-left">{{ formatPercentage(serviceRequestData.msrpPercent) }}</td>
          </tr>
        </tbody>
      </v-table>
    </div>

    <!-- Service Approvals Table -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Service Approvals</h3>
      <div v-if="serviceApprovals.length > 0">
        <v-table density="compact" class="mb-4">
          <thead>
            <tr>
              <th class="text-left">Fixed Term (Months)</th>
              <th class="text-left">Display Name</th>
              <th class="text-left">DSD Quantity</th>
              <th class="text-left">Dealer Quantity</th>
              <th class="text-left">Colour %</th>
              <th class="text-left">Oversize %</th>
              <th class="text-left">CPC Includes Accessory</th>
              <th class="text-left">Black & White</th>
              <th class="text-left">Colour</th>
              <th class="text-left">IPRC</th>
              <th class="text-left">Min Base Amount</th>
              <th class="text-left">Min Base Volume</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(approval, index) in serviceApprovals" :key="index">
              <td>{{ formatValue(approval.fixedTermInMonth) }}</td>
              <td>{{ formatValue(approval.displayName) }}</td>
              <td>{{ formatValue(approval.dsdQuantity) }}</td>
              <td>{{ formatValue(approval.dealerQuantity) }}</td>
              <td>{{ formatPercentage(approval.colourPercentage) }}</td>
              <td>{{ formatPercentage(approval.oversizePercentage) }}</td>
              <td>{{ formatValue(approval.cpcIncludesAccessory) }}</td>
              <td>{{ formatCurrency(approval.blackAndWhite) }}</td>
              <td>{{ formatCurrency(approval.colour) }}</td>
              <td>{{ formatCurrency(approval.iprc) }}</td>
              <td>{{ formatCurrency(approval.minimumBaseAmount) }}</td>
              <td>{{ formatValue(approval.minimumBaseVolume) }}</td>
            </tr>
          </tbody>
        </v-table>
      </div>
      <v-alert v-else type="info" class="mb-4">
        No service approvals available
      </v-alert>
    </div>

    <!-- Current Usage Information -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Current Usage Information</h3>
      <v-card variant="outlined" class="pa-3">
        <div v-if="serviceRequestData.currentUsageInformation">
          {{ serviceRequestData.currentUsageInformation }}
        </div>
        <v-alert v-else type="info" density="compact">
          No current usage information provided
        </v-alert>
      </v-card>
    </div>

    <!-- Current Equipment Information -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Current Equipment Information</h3>
      <v-card variant="outlined" class="pa-3">
        <div v-if="serviceRequestData.currentEquipmentInfo">
          {{ serviceRequestData.currentEquipmentInfo }}
        </div>
        <v-alert v-else type="info" density="compact">
          No current equipment information provided
        </v-alert>
      </v-card>
    </div>

    <!-- Service Business Case -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Service Business Case</h3>
      <v-card variant="outlined" class="pa-3">
        <div v-if="serviceRequestData.serviceBusinessCase">
          {{ serviceRequestData.serviceBusinessCase }}
        </div>
        <v-alert v-else type="info" density="compact">
          No service business case provided
        </v-alert>
      </v-card>
    </div>

  </div>
</template>

<style scoped>
.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}
</style>
