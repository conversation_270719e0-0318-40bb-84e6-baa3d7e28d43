<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Define props - receive data from parent component
const props = defineProps({
  serviceRequestData: {
    type: Object,
    default: () => ({
      service_request_form: {},
      toner_and_service_value_pack: {},
      approvals_details: {},
      product_service_details: { models_overview: [], accessories_included: [] },
      competitive_current_usage_info: {},
      current_equipment_details: {},
      service_business_case: {}
    })
  }
});

// Define types for the service request form and related data
interface ServiceRequestForm {
  request_date?: string;
  sales_representative_name?: string;
  sales_manager_name?: string;
  customer_business_name?: string;
  customer_legal_name?: string;
  address1?: string;
  address2_3?: string;
  city_province_postal?: string;
  [key: string]: any;
}

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Format percentage values
const formatPercentage = (value: number | null | undefined): string => {
  if (!value && value !== 0) return 'N/A';
  return `${value}%`;
};

// Format general values
const formatValue = (value: any): string => {
  if (value === null || value === undefined || value === '') return 'N/A';
  return String(value);
};

// Get region display name
const getRegionDisplay = (regionValue: string | null) => {
  const regionMap: Record<string, string> = {
    'central': 'Central',
    'eastern': 'Eastern', 
    'western': 'Western'
  };
  return regionValue ? regionMap[regionValue] || regionValue : 'N/A';
};

// Computed properties for each section
const headerInfo = computed(() => props.serviceRequestData.service_request_form || {});
const tonerPack = computed(() => props.serviceRequestData.toner_and_service_value_pack || {});
const approvals = computed(() => props.serviceRequestData.serviceApprovals || {});
const productDetails = computed(() => props.serviceRequestData.product_service_details || { models_overview: [], accessories_included: [] });
const competitiveInfo = computed(() => props.serviceRequestData.competitive_current_usage_info || {});
const currentEquipment = computed(() => props.serviceRequestData.current_equipment_details || {});
const businessCase = computed(() => props.serviceRequestData.service_business_case || {});
</script>

<template>
  <div>
    <!-- Header & Basic Request Information -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Basic Request Information</h3>
            <v-table density="compact" class="mb-4">
              <tbody>
                <tr>
                  <th class="text-left" width="200">Request Date</th>
                  <td class="text-left">{{ headerInfo.request_date || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Sales Representative</th>
                  <td class="text-left">{{ headerInfo.sales_representative_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Sales Manager</th>
                  <td class="text-left">{{ headerInfo.sales_manager_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Customer Business Name</th>
                  <td class="text-left">{{ headerInfo.customer_business_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Customer Legal Name</th>
                  <td class="text-left">{{ headerInfo.customer_legal_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Address</th>
                  <td class="text-left">
                    <div v-if="headerInfo.address1 || headerInfo.address2_3 || headerInfo.city_province_postal">
                      <div v-if="headerInfo.address1">{{ headerInfo.address1 }}</div>
                      <div v-if="headerInfo.address2_3">{{ headerInfo.address2_3 }}</div>
                      <div v-if="headerInfo.city_province_postal">{{ headerInfo.city_province_postal }}</div>
                    </div>
                    <span v-else>N/A</span>
                  </td>
                </tr>
              </tbody>
            </v-table>
          </div>

          <!-- Toner and Service Value Pack -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Toner and Service Value Pack</h3>
            <v-table density="compact" class="mb-4">
              <tbody>
                <tr>
                  <th class="text-left" width="200">Toner In/Out</th>
                  <td class="text-left">{{ tonerPack.toner_in_out || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Service Value Pack</th>
                  <td class="text-left">{{ tonerPack.service_value_pack || 'N/A' }}</td>
                </tr>
              </tbody>
            </v-table>
          </div>

          <!-- Approvals and Lease Details -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Approvals and Lease Details</h3>
            <v-table density="compact" class="mb-4">
              <tbody>
                <tr>
                  <th class="text-left" width="200">Region</th>
                  <td class="text-left">{{ getRegionDisplay(approvals.selected_region) }}</td>
                </tr>
                <tr>
                  <th class="text-left">DSD Territory</th>
                  <td class="text-left">{{ formatValue(approvals.dsd_territory) }}</td>
                </tr>
                <tr>
                  <th class="text-left">Dealer Territory</th>
                  <td class="text-left">{{ formatValue(approvals.dealer_territory) }}</td>
                </tr>
                <tr>
                  <th class="text-left">Dealer Accepted Service Rates</th>
                  <td class="text-left">{{ approvals.dealer_accepted_service_rates || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Purchase/Lease Type</th>
                  <td class="text-left">{{ approvals.purchase_lease_type || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Length of Lease (Months)</th>
                  <td class="text-left">{{ approvals.length_of_lease_months || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">MSRP Details</th>
                  <td class="text-left">{{ formatValue(approvals.msrp_details) }}</td>
                </tr>
              </tbody>
            </v-table>
          </div>

          <!-- Product/Service Details -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Product/Service Details</h3>
            <div v-if="productDetails.models_overview?.length > 0">
              <v-table density="compact" class="mb-4">
                <thead>
                  <tr>
                    <th class="text-left">Model</th>
                    <th class="text-left">DSD Qty</th>
                    <th class="text-left">Dealer Qty</th>
                    <th class="text-left">Est. AMV/Unit</th>
                    <th class="text-left">Color %</th>
                    <th class="text-left">Oversize %</th>
                    <th class="text-left">Fees in CPC</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(product, index) in productDetails.models_overview" :key="index">
                    <td>{{ product.model || 'N/A' }}</td>
                    <td>{{ product.dsd_qty || 'N/A' }}</td>
                    <td>{{ product.dealer_qty || 'N/A' }}</td>
                    <td>{{ formatCurrency(product.estimated_amv_unit) }}</td>
                    <td>{{ formatPercentage(product.colour_percentage) }}</td>
                    <td>{{ formatPercentage(product.oversize_percentage) }}</td>
                    <td>{{ formatValue(product.fees_included_in_cpc) }}</td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <v-alert v-else type="info" class="mb-4">
              No product details available
            </v-alert>

            <!-- Accessories -->
            <h4 class="text-subtitle-2 mb-2">Accessories Included</h4>
            <div v-if="productDetails.accessories_included?.length > 0">
              <v-table density="compact" class="mb-4">
                <thead>
                  <tr>
                    <th class="text-left">MFP Model</th>
                    <th class="text-left">Accessory Name</th>
                    <th class="text-left">Published Rate</th>
                    <th class="text-left">Requested Rate</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(accessory, index) in productDetails.accessories_included" :key="index">
                    <td>{{ accessory.mfpModelName || 'N/A' }}</td>
                    <td>{{ accessory.accessoryName || 'N/A' }}</td>
                    <td>{{ formatCurrency(accessory.publishedRate) }}</td>
                    <td>{{ formatCurrency(accessory.requestedRate) }}</td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <v-alert v-else type="info" class="mb-4">
              No accessories included
            </v-alert>
          </div>

          <!-- Competitive & Current Usage Information -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Competitive & Current Usage Information</h3>
            <v-card variant="outlined" class="pa-3">
              <div v-if="competitiveInfo.details">
                {{ competitiveInfo.details }}
              </div>
              <v-alert v-else type="info" density="compact">
                No competitive information provided
              </v-alert>
            </v-card>
          </div>

          <!-- Current Equipment Details -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Current Equipment Details</h3>
            <v-card variant="outlined" class="pa-3">
              <div v-if="currentEquipment.equipment_list">
                {{ currentEquipment.equipment_list }}
              </div>
              <v-alert v-else type="info" density="compact">
                No current equipment details provided
              </v-alert>
            </v-card>
          </div>

          <!-- Service Business Case -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Service Business Case</h3>
            <v-card variant="outlined" class="pa-3">
              <div v-if="businessCase.justification">
                {{ businessCase.justification }}
              </div>
              <v-alert v-else type="info" density="compact">
                No business case justification provided
              </v-alert>
            </v-card>
          </div>

  </div>
</template>

<style scoped>
.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}
</style>
