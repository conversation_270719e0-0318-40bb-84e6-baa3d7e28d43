<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { getServiceForm } from '@/services/salesRequestService';

const { t } = useI18n();

// Define props - only need requestId and serviceFormId to fetch data
const props = defineProps({
  requestId: {
    type: [Number, String],
    required: false
  },
  serviceFormId: {
    type: [Number, String],
    required: false
  },
  isExpanded: {
    type: Boolean,
    default: false
  }
});

// Define types for the service request form and related data
interface ServiceRequestForm {
  request_date?: string;
  sales_representative_name?: string;
  sales_manager_name?: string;
  customer_business_name?: string;
  customer_legal_name?: string;
  address1?: string;
  address2_3?: string;
  city_province_postal?: string;
  [key: string]: any;
}

interface ServiceRequestData {
  service_request_form: ServiceRequestForm;
  toner_and_service_value_pack: Record<string, any>;
  approvals_details: Record<string, any>;
  product_service_details: { models_overview: any[]; accessories_included: any[] };
  competitive_current_usage_info: Record<string, any>;
  current_equipment_details: Record<string, any>;
  service_business_case: Record<string, any>;
}

// Internal state for service request data
const serviceRequestData = ref<ServiceRequestData>({
  service_request_form: {},
  toner_and_service_value_pack: {},
  approvals_details: {},
  product_service_details: { models_overview: [], accessories_included: [] },
  competitive_current_usage_info: {},
  current_equipment_details: {},
  service_business_case: {}
});

const isLoading = ref(false);
const hasLoaded = ref(false);
const loadError = ref('');

// Function to fetch service form data
const fetchServiceFormData = async () => {
  if (!props.serviceFormId || hasLoaded.value || isLoading.value) {
    return;
  }

  isLoading.value = true;
  loadError.value = '';

  try {
    const response = await getServiceForm(Number(props.serviceFormId));
    serviceRequestData.value = response.data || serviceRequestData.value;
    hasLoaded.value = true;
  } catch (error) {
    console.error('Failed to fetch service form data:', error);
    loadError.value = 'Failed to load service request data';
  } finally {
    isLoading.value = false;
  }
};

// Watch for expansion state changes to trigger data loading
watch(() => props.isExpanded, (newValue) => {
  if (newValue && props.serviceFormId) {
    fetchServiceFormData();
  }
}, { immediate: true });

// Also watch for serviceFormId changes
watch(() => props.serviceFormId, (newValue) => {
  if (newValue && props.isExpanded) {
    hasLoaded.value = false; // Reset to allow refetch
    fetchServiceFormData();
  }
});

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Format percentage values
const formatPercentage = (value: number | null | undefined): string => {
  if (!value && value !== 0) return 'N/A';
  return `${value}%`;
};

// Format general values
const formatValue = (value: any): string => {
  if (value === null || value === undefined || value === '') return 'N/A';
  return String(value);
};

// Get region display name
const getRegionDisplay = (regionValue: string | null) => {
  const regionMap: Record<string, string> = {
    'central': 'Central',
    'eastern': 'Eastern', 
    'western': 'Western'
  };
  return regionValue ? regionMap[regionValue] || regionValue : 'N/A';
};

// Computed properties for each section
const headerInfo = computed(() => serviceRequestData.value.service_request_form || {});
const tonerPack = computed(() => serviceRequestData.value.toner_and_service_value_pack || {});
const approvals = computed(() => serviceRequestData.value.approvals_details || {});
const productDetails = computed(() => serviceRequestData.value.product_service_details || { models_overview: [], accessories_included: [] });
const competitiveInfo = computed(() => serviceRequestData.value.competitive_current_usage_info || {});
const currentEquipment = computed(() => serviceRequestData.value.current_equipment_details || {});
const businessCase = computed(() => serviceRequestData.value.service_business_case || {});
</script>

<template>
  <v-card class="mb-4">
    <v-expansion-panels>
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">business</v-icon>
            <span class="text-h6">Service Request Details</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          
          <!-- Header & Basic Request Information -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Basic Request Information</h3>
            <v-table density="compact" class="mb-4">
              <tbody>
                <tr>
                  <th class="text-left" width="200">Request Date</th>
                  <td class="text-left">{{ headerInfo.request_date || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Sales Representative</th>
                  <td class="text-left">{{ headerInfo.sales_representative_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Sales Manager</th>
                  <td class="text-left">{{ headerInfo.sales_manager_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Customer Business Name</th>
                  <td class="text-left">{{ headerInfo.customer_business_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Customer Legal Name</th>
                  <td class="text-left">{{ headerInfo.customer_legal_name || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Address</th>
                  <td class="text-left">
                    <div v-if="headerInfo.address1 || headerInfo.address2_3 || headerInfo.city_province_postal">
                      <div v-if="headerInfo.address1">{{ headerInfo.address1 }}</div>
                      <div v-if="headerInfo.address2_3">{{ headerInfo.address2_3 }}</div>
                      <div v-if="headerInfo.city_province_postal">{{ headerInfo.city_province_postal }}</div>
                    </div>
                    <span v-else>N/A</span>
                  </td>
                </tr>
              </tbody>
            </v-table>
          </div>

          <!-- Toner and Service Value Pack -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Toner and Service Value Pack</h3>
            <v-table density="compact" class="mb-4">
              <tbody>
                <tr>
                  <th class="text-left" width="200">Toner In/Out</th>
                  <td class="text-left">{{ tonerPack.toner_in_out || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Service Value Pack</th>
                  <td class="text-left">{{ tonerPack.service_value_pack || 'N/A' }}</td>
                </tr>
              </tbody>
            </v-table>
          </div>

          <!-- Approvals and Lease Details -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Approvals and Lease Details</h3>
            <v-table density="compact" class="mb-4">
              <tbody>
                <tr>
                  <th class="text-left" width="200">Region</th>
                  <td class="text-left">{{ getRegionDisplay(approvals.selected_region) }}</td>
                </tr>
                <tr>
                  <th class="text-left">DSD Territory</th>
                  <td class="text-left">{{ formatValue(approvals.dsd_territory) }}</td>
                </tr>
                <tr>
                  <th class="text-left">Dealer Territory</th>
                  <td class="text-left">{{ formatValue(approvals.dealer_territory) }}</td>
                </tr>
                <tr>
                  <th class="text-left">Dealer Accepted Service Rates</th>
                  <td class="text-left">{{ approvals.dealer_accepted_service_rates || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Purchase/Lease Type</th>
                  <td class="text-left">{{ approvals.purchase_lease_type || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">Length of Lease (Months)</th>
                  <td class="text-left">{{ approvals.length_of_lease_months || 'N/A' }}</td>
                </tr>
                <tr>
                  <th class="text-left">MSRP Details</th>
                  <td class="text-left">{{ formatValue(approvals.msrp_details) }}</td>
                </tr>
              </tbody>
            </v-table>
          </div>

          <!-- Product/Service Details -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Product/Service Details</h3>
            <div v-if="productDetails.models_overview?.length > 0">
              <v-table density="compact" class="mb-4">
                <thead>
                  <tr>
                    <th class="text-left">Model</th>
                    <th class="text-left">DSD Qty</th>
                    <th class="text-left">Dealer Qty</th>
                    <th class="text-left">Est. AMV/Unit</th>
                    <th class="text-left">Color %</th>
                    <th class="text-left">Oversize %</th>
                    <th class="text-left">Fees in CPC</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(product, index) in productDetails.models_overview" :key="index">
                    <td>{{ product.model || 'N/A' }}</td>
                    <td>{{ product.dsd_qty || 'N/A' }}</td>
                    <td>{{ product.dealer_qty || 'N/A' }}</td>
                    <td>{{ formatCurrency(product.estimated_amv_unit) }}</td>
                    <td>{{ formatPercentage(product.colour_percentage) }}</td>
                    <td>{{ formatPercentage(product.oversize_percentage) }}</td>
                    <td>{{ formatValue(product.fees_included_in_cpc) }}</td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <v-alert v-else type="info" class="mb-4">
              No product details available
            </v-alert>

            <!-- Accessories -->
            <h4 class="text-subtitle-2 mb-2">Accessories Included</h4>
            <div v-if="productDetails.accessories_included?.length > 0">
              <v-table density="compact" class="mb-4">
                <thead>
                  <tr>
                    <th class="text-left">MFP Model</th>
                    <th class="text-left">Accessory Name</th>
                    <th class="text-left">Published Rate</th>
                    <th class="text-left">Requested Rate</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(accessory, index) in productDetails.accessories_included" :key="index">
                    <td>{{ accessory.mfpModelName || 'N/A' }}</td>
                    <td>{{ accessory.accessoryName || 'N/A' }}</td>
                    <td>{{ formatCurrency(accessory.publishedRate) }}</td>
                    <td>{{ formatCurrency(accessory.requestedRate) }}</td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <v-alert v-else type="info" class="mb-4">
              No accessories included
            </v-alert>
          </div>

          <!-- Competitive & Current Usage Information -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Competitive & Current Usage Information</h3>
            <v-card variant="outlined" class="pa-3">
              <div v-if="competitiveInfo.details">
                {{ competitiveInfo.details }}
              </div>
              <v-alert v-else type="info" density="compact">
                No competitive information provided
              </v-alert>
            </v-card>
          </div>

          <!-- Current Equipment Details -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Current Equipment Details</h3>
            <v-card variant="outlined" class="pa-3">
              <div v-if="currentEquipment.equipment_list">
                {{ currentEquipment.equipment_list }}
              </div>
              <v-alert v-else type="info" density="compact">
                No current equipment details provided
              </v-alert>
            </v-card>
          </div>

          <!-- Service Business Case -->
          <div class="mb-6">
            <h3 class="text-subtitle-1 mb-3 text-primary">Service Business Case</h3>
            <v-card variant="outlined" class="pa-3">
              <div v-if="businessCase.justification">
                {{ businessCase.justification }}
              </div>
              <v-alert v-else type="info" density="compact">
                No business case justification provided
              </v-alert>
            </v-card>
          </div>

        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<style scoped>
.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}
</style>
