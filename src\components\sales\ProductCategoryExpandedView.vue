<script setup lang="ts">
import { PropType, computed, ref, watch } from 'vue';

// Define the type for a product item field (mirroring parent's structure)
interface ProductField {
  type: string;
  value: string | number;
  readonly?: boolean;
  min?: number;
  max?: number;
}

// Define the type for a product item (mirroring parent's structure)
interface ProductItem {
  productName: ProductField;
  dsdQuantity: ProductField;
  dealerITsQuantity: ProductField;
  itemNumber: ProductField;
  requestSellingPrice: ProductField;
  msrp: ProductField;
  percentOfMsrp: ProductField;
  subProducts: ProductItem[];
}

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  products: {
    type: Array as PropType<ProductItem[]>,
    required: true,
  },
  productType: {
    type: String as PropType<'hardware' | 'software'>,
    required: true,
  },
  productOptions: {
    type: Array as PropType<{ title: string; value: string }[]>,
    required: true,
  },
  getSubProductOptionsFunc: {
    type: Function as PropType<(parentValue: string) => { title: string; value: string }[]>,
    required: true,
  },
  updateProductDetailsFunc: {
    type: Function as PropType<(product: ProductItem, productValue: string, isSubProduct?: boolean) => void>,
    required: true,
  },
  handlePercentChangeFunc: {
    type: Function as PropType<(product: ProductItem) => void>,
    required: true,
  },
  removeProductFunc: {
    type: Function as PropType<(type: 'hardware' | 'software', index: number) => void>,
    required: true,
  },
  addSubProductFunc: {
    type: Function as PropType<(type: 'hardware' | 'software', productIndex: number) => void>,
    required: true,
  },
  removeSubProductFunc: {
    type: Function as PropType<(type: 'hardware' | 'software', productIndex: number, subProductIndex: number) => void>,
    required: true,
  },
  addProductToListFunc: { // Renamed from addProduct to avoid potential naming conflicts
    type: Function as PropType<(type: 'hardware' | 'software') => void>,
    required: true,
  },
  calculateProductSummaryFunc: {
    type: Function as PropType<(product: ProductItem) => { totalMsrp: number; totalSellingPrice: number; avgPercent: string | number }>,
    required: true,
  },
  t: {
    type: Function as PropType<(key: string, params?: Record<string, any>) => string>,
    required: true,
  },
});

// Reactive array holding indices of expanded panels. For `multiple` expansion mode, this should be an array.
const expandedPanels = ref<number[]>([]);

// Whenever a new product is pushed to the list, automatically expand its panel (last index)

watch(
  () => props.products.length,
  (newLength) => {
    if (newLength > 0) {
      const lastIndex = newLength - 1;
      // Only add the new index if it's not already in the array
      // This prevents issues if the user manually collapses and then a new item is added
      if (!expandedPanels.value.includes(lastIndex)) {
        // Directly mutate the array. This is often more stable for v-model with components.
        expandedPanels.value.push(lastIndex);
      }
    }
  }
);

const onDsdQuantityChange = (product: ProductItem, val: string | number) => {
  product.dsdQuantity.value = Math.max(1, Number(val) || 1);
};

const onDealerItsQuantityChange = (product: ProductItem, val: string | number) => {
  product.dealerITsQuantity.value = Math.max(0, Number(val) || 0);
};

</script>

<template>
  <v-card class="mb-6" variant="outlined">
    <v-card-title class="text-subtitle-1 bg-grey-lighten-3">{{ props.title }}</v-card-title>
    <v-card-text>
      <template v-if="props.products.length === 0">
        <p class="text-center pa-4">{{ props.t('page.sales_request_form.worksheets.messages.no_items_added', { item_type: props.title.toLowerCase() }) }}</p>
      </template>
      <v-expansion-panels v-model="expandedPanels" multiple variant="outlined" class="mb-4 mt-2">
        <v-expansion-panel
          v-for="(product, productIndex) in props.products"
          :key="`${props.productType}-exp-${productIndex}`"
          class="mb-2"
          :class="{'bg-grey-lighten-5': productIndex % 2 !== 0}" 
        >
          <v-expansion-panel-title :class="{'bg-grey-lighten-4': productIndex % 2 === 0}" hide-actions>
            <template v-slot:default="{ expanded }">
              <v-row no-gutters class="align-center w-100">
                <v-col cols="auto">
                  <h3 class="text-subtitle-1">{{ props.t('page.sales_request_form.worksheets.main_unit_title', { index: productIndex + 1 }) }}</h3>
                </v-col>
                <v-col cols="auto" class="ml-4">
                  <v-radio-group
                    v-model="product.proposalType.value"
                    inline
                    density="compact"
                    hide-details
                    @click.stop
                  >
                    <v-radio :label="props.t('page.sales_request_form.worksheets.proposal_type_primary')" value="primary"></v-radio>
                    <v-radio :label="props.t('page.sales_request_form.worksheets.proposal_type_optional')" value="alternate"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-spacer></v-spacer>
                <v-col cols="auto">
                  <v-fade-transition leave-absolute>
                    <div v-if="expanded" key="buttons" @click.stop>
                      
                      <v-btn size="small" color="error" @click="props.removeProductFunc(props.productType, productIndex)" prepend-icon="delete">
                        {{ props.t('page.sales_request_form.worksheets.buttons.remove_main_unit') }}
                      </v-btn>
                    </div>
                  </v-fade-transition>
                </v-col>
              </v-row>
            </template>
          </v-expansion-panel-title>
          <v-expansion-panel-text class="pa-0"> <!-- Remove default padding from panel text -->
            <div class="product-content-wrapper pa-4"> <!-- Add custom padding wrapper -->
              <!-- Main Product Fields -->
              <v-row class="pb-4">
                <v-col cols="12" md="6" lg="4">
                  <v-select
                    v-model="product.productName.value"
                    :items="props.productOptions"
                    :label="props.productType === 'software' ? props.t('page.sales_request_form.worksheets.fields.solution_category_name.label') : props.t('page.sales_request_form.worksheets.fields.product_name.label')"
                    item-title="title"
                    item-value="value"
                    density="compact"
                    variant="outlined"
                    hide-details="auto"
                    @update:model-value="(val: string) => props.updateProductDetailsFunc(product, val)"
                  ></v-select>
                </v-col>
                <template v-if="props.productType !== 'software'">
                <v-col cols="12" md="6" lg="3">
                  <v-text-field
                    v-model="product.itemNumber.value"
                    :label="props.t('page.sales_request_form.worksheets.fields.item_number.label')"
                    density="compact"
                    variant="outlined"
                    readonly
                    hide-details="auto"
                  ></v-text-field>
                </v-col>
                  <v-col cols="12" md="3" lg="2">
                    <v-text-field
                      v-model="product.dsdQuantity.value"
                      :label="props.t('page.sales_request_form.worksheets.fields.dsd_quantity.label')"
                      type="number"
                      :min="1"
                      density="compact"
                      variant="outlined"
                      hide-details="auto"
                      @update:model-value="(val) => onDsdQuantityChange(product, val)"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3" lg="2">
                    <v-text-field
                      v-model="product.dealerITsQuantity.value"
                      :label="props.t('page.sales_request_form.worksheets.fields.dealer_its_quantity.label')"
                      type="number"
                      :min="0"
                      density="compact"
                      variant="outlined"
                      hide-details="auto"
                      @update:model-value="(val) => onDealerItsQuantityChange(product, val)"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="2">
                    <v-text-field
                      v-model="product.msrp.value"
                      :label="props.t('page.sales_request_form.worksheets.fields.msrp.label')"
                      density="compact"
                      variant="outlined"
                      prefix="$"
                      hide-details="auto"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="3">
                    <v-text-field
                      v-model="product.percentOfMsrp.value"
                      :label="props.t('page.sales_request_form.worksheets.fields.percent_of_msrp.label')"
                      type="number"
                      min="1"
                      max="100"
                      suffix="%"
                      density="compact"
                      variant="outlined"
                      hide-details="auto"
                      @update:model-value="() => props.handlePercentChangeFunc(product)"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="3">
                    <v-text-field
                      v-model="product.requestSellingPrice.value"
                      :label="props.t('page.sales_request_form.worksheets.fields.request_selling_price.label')"
                      density="compact"
                      variant="outlined"
                      readonly
                      prefix="$"
                      hide-details="auto"
                    ></v-text-field>
                  </v-col>
                </template>
              </v-row>

              <!-- Sub Products -->
              <div v-if="product.subProducts.length > 0" class="sub-products mt-2 mb-4">
                <div v-for="(subProduct, subIndex) in product.subProducts" :key="`${props.productType}-exp-sub-${productIndex}-${subIndex}`" class="sub-product-container mb-4">
                  <div class="sub-product-header pa-2 d-flex justify-space-between align-center" :class="{'bg-blue-lighten-5': subIndex % 2 === 0}">
                    <h4 class="text-subtitle-2">{{ props.productType === 'software' ? props.t('page.sales_request_form.worksheets.solution_item_title', { index: subIndex + 1}) : props.t('page.sales_request_form.worksheets.accessory_title', { index: subIndex + 1}) }}</h4>
                    <v-btn size="small" color="error" @click="props.removeSubProductFunc(props.productType, productIndex, subIndex)" prepend-icon="delete">
                      {{ props.t('page.sales_request_form.worksheets.buttons.remove') }}
                    </v-btn>
                  </div>
                  <v-row class="pa-4">
                     <v-col cols="12" md="6" lg="4">
                      <v-select
                        v-model="subProduct.productName.value"
                        :items="props.getSubProductOptionsFunc(product.productName.value.toString())"
                        :label="props.t('page.sales_request_form.worksheets.fields.product_name.label')"
                        item-title="title"
                        item-value="value"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:model-value="(val: string) => props.updateProductDetailsFunc(subProduct, val, true)"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="6" lg="2">
                      <v-text-field
                        v-model="subProduct.itemNumber.value"
                        :label="props.t('page.sales_request_form.worksheets.fields.item_number.label')"
                        density="compact"
                        variant="outlined"
                        readonly
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" lg="2">
                      <v-text-field
                        v-model="subProduct.dsdQuantity.value"
                        :label="props.t('page.sales_request_form.worksheets.fields.dsd_quantity.label')"
                        type="number"
                        :min="1"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:model-value="(val) => onDsdQuantityChange(subProduct, val)"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" lg="2">
                      <v-text-field
                        v-model="subProduct.dealerITsQuantity.value"
                        :label="props.t('page.sales_request_form.worksheets.fields.dealer_its_quantity.label')"
                        type="number"
                        :min="0"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:model-value="(val) => onDealerItsQuantityChange(subProduct, val)"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" lg="2">
                      <v-text-field
                        v-model="subProduct.msrp.value"
                        :label="props.t('page.sales_request_form.worksheets.fields.msrp.label')"
                        density="compact"
                        variant="outlined"
                        readonly
                        prefix="$"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" lg="3">
                      <v-text-field
                        v-model="subProduct.percentOfMsrp.value"
                        :label="props.t('page.sales_request_form.worksheets.fields.percent_of_msrp.label')"
                        type="number"
                        min="1"
                        max="100"
                        suffix="%"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:model-value="() => props.handlePercentChangeFunc(subProduct)"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" lg="3">
                      <v-text-field
                        v-model="subProduct.requestSellingPrice.value"
                        :label="props.t('page.sales_request_form.worksheets.fields.request_selling_price.label')"
                        density="compact"
                        variant="outlined"
                        readonly
                        prefix="$"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </div>
              </div>
              <!-- Add Accessory/Solution Item Button (moved to bottom of sub-products) -->
              <div class="d-flex justify-end mb-2">
                <v-btn
                  prepend-icon="add"
                  size="small"
                  color="secondary"
                  @click="props.addSubProductFunc(props.productType, productIndex)"
                >
                  {{ props.productType === 'software' ? props.t('page.sales_request_form.worksheets.buttons.add_solution_item') : props.t('page.sales_request_form.worksheets.buttons.add_accessory') }}
                </v-btn>
              </div>

              <!-- Product Summary -->
              <v-row class="mt-2 pa-2 bg-grey-lighten-4">
                <v-col cols="12">
                  <h4 class="text-subtitle-1 mb-2">{{ props.t('page.sales_request_form.worksheets.summary_for_main_unit_title', { index: productIndex + 1}) }}</h4>
                </v-col>
                <v-col cols="12" md="4">
                  <p><strong>{{ props.t('page.sales_request_form.worksheets.total_msrp_label') }}</strong> ${{ props.calculateProductSummaryFunc(product).totalMsrp.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</p>
                </v-col>
                <v-col cols="12" md="4">
                  <p><strong>{{ props.t('page.sales_request_form.worksheets.total_selling_price_label') }}</strong> ${{ props.calculateProductSummaryFunc(product).totalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</p>
                </v-col>
                <v-col cols="12" md="4">
                  <p><strong>{{ props.t('page.sales_request_form.worksheets.avg_percent_msrp_label') }}</strong> {{ props.calculateProductSummaryFunc(product).avgPercent }}%</p>
                </v-col>
              </v-row>
            </div> <!-- End product-content-wrapper -->
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
      
      <!-- Add Product Button -->
      <div class="d-flex justify-end mt-4">
        <v-btn color="primary" @click="props.addProductToListFunc(props.productType)" prepend-icon="add">
          {{ props.t('page.sales_request_form.worksheets.buttons.add_main_unit_typed', { unit_type: props.title }) }}
        </v-btn>
      </div>
    </v-card-text>
  </v-card>
</template>

<style scoped>
/* .product-container styles removed as v-expansion-panel handles its own appearance. */
/* The class .product-container is no longer used in the template. */

.product-content-wrapper {
  /* This class is used to wrap the content within v-expansion-panel-text */
  /* to allow for consistent padding if v-expansion-panel-text has its padding removed. */
}

.sub-product-container {
  border: 1px solid #e0e0e0; 
  border-radius: 4px;
}

.sub-products {
  padding-left: 16px; 
  border-left: 2px solid #cfd8dc; 
}
</style>
