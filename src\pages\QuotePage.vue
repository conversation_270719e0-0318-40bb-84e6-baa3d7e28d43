<template>
  <v-container fluid>
    <!-- Header Section -->
    <v-row class="mb-4 align-center">
      <v-col cols="12" md="2">
        <v-img src="/canon-logo.jpg" alt="Canon Logo" contain height="50"></v-img>
      </v-col>
      <v-col cols="12" md="7">
        <h2 class="text-h5">{{ data.documentHeader.customerName }}</h2>
        <div class="text-caption">
          CMAC Account: {{ data.documentHeader.cmacAccountNumber }} |
          Pricing Issued: {{ formatDate(data.documentHeader.pricingIssuedOn) }} |
          Price Validity: {{ formatDate(data.documentHeader.priceValidity) }} |
          CMAC Account: {{ data.documentHeader.cmacAccountNumber }} <!-- Ensuring CMAC is clearly visible as in Excel -->
        </div>

        <v-chip v-if="data.documentHeader.competitiveKO"
                :color="data.documentHeader.competitiveKO.status === 'NO' ? 'green' : 'red'"
                size="small" class="mt-1">
          Competitive KO: {{ data.documentHeader.competitiveKO.status }}
          <v-tooltip activator="parent" location="bottom">{{ data.documentHeader.competitiveKO.details }}</v-tooltip>
        </v-chip>
      </v-col>
      <v-col cols="12" md="3" class="text-md-right">
        <v-card variant="outlined" class="pa-2">
          <div class="text-caption">Total MSRP</div>
          <div class="text-h6 font-weight-bold">{{ formatCurrency(data.summaryTotals.totalMSRP) }}</div>
          <div class="text-caption mt-1">Total Approved Pricing</div>
          <div class="text-h6 font-weight-bold">{{ formatCurrency(data.summaryTotals.totalApprovedPricing) }}</div>
        </v-card>
        <div class="d-flex flex-column" style="gap: 8px;">
          <v-btn 
            color="success" 
            @click="onApprove" 
            prepend-icon="check"
            size="large"
          >
            Approve Pricing
          </v-btn>
          <v-btn 
            color="primary" 
            @click="exportQuote" 
            prepend-icon="download"
            variant="outlined"
          >
            Export to Excel
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Key Instructions / Price Desk Comments -->
    <v-row>
      <v-col cols="12">
        <v-textarea
          v-model="priceDeskComments"
          label="Price Desk Comments / Key Instructions"
          variant="outlined"
          rows="2"
          auto-grow
          class="mb-4"
        ></v-textarea>
      </v-col>
    </v-row>

    <!-- Product Configurations -->
    <section-card title="Product Configuration">
      <v-expansion-panels variant="accordion" class="mt-2">
        <v-expansion-panel
          v-for="group in data.productGroups"
          :key="group.id"
          :title="group.groupName"
        >
          <v-expansion-panel-text>
            <v-table density="compact" class="mb-2">
              <thead>
                <tr>
                  <th class="text-left">Product Description</th>
                  <th class="text-left">Item No.</th>
                  <th class="text-right">DSD Qty</th>
                  <th class="text-right">Dealer territory Qty</th>
                  <th class="text-right">Total Qty</th>
                  <th class="text-right">MSRP/Unit</th>
                  <th class="text-right">Approved Price/Unit</th>
                  <th class="text-right">Approved (% MSRP)</th>
                </tr>
              </thead>
              <tbody>
                <!-- Main Item -->
                <tr class="font-weight-bold bg-grey-lighten-4">
                  <td>{{ group.mainItem.productDescription }}</td>
                  <td>{{ group.mainItem.itemNumber }}</td>
                  <td class="text-right">{{ group.mainItem.dsdQty }}</td>
                  <td class="text-right">{{ group.mainItem.dealerTerritoryQty !== undefined ? group.mainItem.dealerTerritoryQty : '-' }}</td>
                  <td class="text-right">{{ group.mainItem.totalQty }}</td>
                  <td class="text-right">{{ formatCurrency(group.mainItem.msrp) }}</td>
                  <td class="text-right">{{ formatCurrency(group.mainItem.approvedSellingPrice) }}</td>
                  <td class="text-right">{{ group.mainItem.approvedSellingPricePercentMSRP }}%</td>
                </tr>
                <!-- Accessories -->
                <tr v-for="(acc, index) in group.accessories" :key="index">
                  <td class="pl-6">{{ acc.productDescription }}</td>
                  <td>{{ acc.itemNumber }}</td>
                  <td class="text-right">{{ acc.dsdQty }}</td>
                  <td class="text-right">{{ acc.dealerTerritoryQty !== undefined ? acc.dealerTerritoryQty : '-' }}</td>
                  <td class="text-right">{{ acc.totalQty }}</td>
                  <td class="text-right">{{ formatCurrency(acc.msrp) }}</td>
                  <td class="text-right">{{ formatCurrency(acc.approvedSellingPrice) }}</td>
                  <td class="text-right">{{ acc.approvedSellingPricePercentMSRP }}%</td>
                </tr>
                <!-- Group Totals -->
                <tr class="font-weight-bold bg-blue-grey-lighten-5">
                  <td colspan="5" class="text-right">Group Totals:</td>
                  <td class="text-right">{{ formatCurrency(group.groupTotal.msrp) }}</td>
                  <td class="text-right">{{ formatCurrency(group.groupTotal.approvedSellingPrice) }}</td>
                  <td class="text-right">{{ group.groupTotal.approvedSellingPricePercentMSRP !== undefined ? group.groupTotal.approvedSellingPricePercentMSRP + '%' : '-' }}</td>
                </tr>
              </tbody>
            </v-table>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </section-card>

    <!-- Software Licensing -->
    <section-card title="Software Licensing">
       <v-expansion-panels variant="accordion" class="mt-2">
        <v-expansion-panel
          v-for="category in data.softwareLicensing"
          :key="category.id"
          :title="category.category"
        >
          <v-expansion-panel-text>
            <v-table density="compact" class="mb-2">
              <thead>
                <tr>
                  <th class="text-left">Product Description</th>
                  <th class="text-left">Item No.</th>
                  <th class="text-right">DSD Qty</th>
                  <th class="text-right">Dealer territory Qty</th>
                  <th class="text-right">Total Qty</th>
                  <th class="text-right">MSRP/Unit</th>
                  <th class="text-right">Approved Price/Unit</th>
                  <th class="text-right">Approved (% MSRP)</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in category.items" :key="index">
                  <td>{{ item.productDescription }}</td>
                  <td>{{ item.itemNumber }}</td>
                  <td class="text-right">{{ item.dsdQty }}</td>
                  <td class="text-right">{{ item.dealerTerritoryQty !== undefined ? item.dealerTerritoryQty : '-' }}</td>
                  <td class="text-right">{{ item.totalQty }}</td>
                  <td class="text-right">{{ formatCurrency(item.msrp) }}</td>
                  <td class="text-right">{{ formatCurrency(item.approvedSellingPrice) }}</td>
                  <td class="text-right">{{ item.approvedSellingPricePercentMSRP }}%</td>
                </tr>
                <!-- Category Totals -->
                <tr class="font-weight-bold bg-blue-grey-lighten-5">
                  <td colspan="5" class="text-right">Category Totals:</td>
                  <td class="text-right">{{ formatCurrency(category.categoryTotal.msrp) }}</td>
                  <td class="text-right">{{ formatCurrency(category.categoryTotal.approvedSellingPrice) }}</td>
                  <td class="text-right">{{ category.categoryTotal.approvedSellingPricePercentMSRP }}%</td>
                </tr>
              </tbody>
            </v-table>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </section-card>

    <!-- Terms and Conditions for Hardware Price Program -->
    <section-card title="Terms and conditions for the hardware price program">
      <v-list density="compact">
        <v-list-item>
          <v-list-item-title>Minimum commitment:</v-list-item-title>
          <v-list-item-subtitle>
            {{ formatCurrency(data.hardwarePriceProgramTerms.minimumCommitment.value) }} ({{ data.hardwarePriceProgramTerms.minimumCommitment.currency }})
            - {{ data.hardwarePriceProgramTerms.minimumCommitment.condition }}
          </v-list-item-subtitle>
        </v-list-item>
        <v-list-item>
          <v-list-item-title>Chargebacks:</v-list-item-title>
          <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.chargebacks }}</v-list-item-subtitle>
        </v-list-item>
         <v-list-item>
          <v-list-item-title>Validity:</v-list-item-title>
          <v-list-item-subtitle>{{ formatDate(data.hardwarePriceProgramTerms.validity) }}</v-list-item-subtitle>
        </v-list-item>
         <v-list-item>
          <v-list-item-title>CMAC Account number:</v-list-item-title>
          <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.cmacAccountNumber }}</v-list-item-subtitle>
        </v-list-item>
        <!-- <v-list-item>
          <v-list-item-title>Equipment eligibility:</v-list-item-title>
          <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.equipmentEligibility }}</v-list-item-subtitle>
        </v-list-item> -->
        <v-list-item>
          <v-list-item-title>Equipment exclusions:</v-list-item-title>
          <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.equipmentExclusions }}</v-list-item-subtitle>
        </v-list-item>
      </v-list>
    </section-card>

    <!-- DSD Territory Service Rates -->
    <section-card title="DSD Territory Service Rates">
      <!-- Note: The keyInstruction, serviceValuePack, fixedServiceRateTerm, and colourModels table are already here -->
      <v-textarea
        v-model="serviceComments"
        label="Service Approval Comments"
        variant="outlined"
        rows="2"
        auto-grow
        class="mb-3"
      ></v-textarea>
      <v-row class="my-3 align-center">
        <v-col cols="auto">
          <span class="font-weight-bold text-subtitle-1 mr-1">Service Value Pack:</span>
          <v-chip color="primary" variant="elevated" size="large">{{ data.dsdTerritoryServiceRates.serviceValuePack }}</v-chip>
        </v-col>
        <v-col cols="auto">
          <span class="font-weight-bold text-subtitle-1 mr-1">Fixed Service Rate Term:</span>
          <span class="font-weight-bold text-subtitle-1">{{ data.dsdTerritoryServiceRates.fixedServiceRateTerm.duration }} {{ data.dsdTerritoryServiceRates.fixedServiceRateTerm.unit }}</span>
        </v-col>
      </v-row>
      <v-alert density="compact" type="info" variant="tonal" class="mb-3 text-caption" border="start">
        Please Note: Service Options CANNOT be Combined. All Machines MUST be priced entirely under ONE service option.
      </v-alert>

      <v-table density="compact" class="mt-2">
        <thead>
          <tr>
            <th>Model Name</th>
            <th class="text-right">Machine Qty</th>
            <th class="text-right">Std B/W CPC</th>
            <th class="text-right">Std Colour CPC</th>
            <th class="text-right">Oversize B/W Rate (per unit)</th>
            <th class="text-right">Extra Long Colour Rate (per unit)</th>
            <th class="text-center">Minimums Applicable</th>
            <th class="text-center">Accessory Fees Included in CPC</th>
            <th class="text-left">Base B&W / Colour Volume</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="model in data.dsdTerritoryServiceRates.colourModels" :key="model.modelName">
            <td>{{ model.modelName }}</td>
            <td class="text-right">{{ model.machineQuantity }}</td>
            <td class="text-right">{{ formatSmallCurrency(model.standardBWCpcRate) }}</td>
            <td class="text-right">{{ formatSmallCurrency(model.standardColourCpcRate) }}</td>
            <td class="text-right">{{ model.extraLongBwRate ? formatSmallCurrency(model.extraLongBwRate) : '-' }}</td>
            <td class="text-right">{{ model.extraLongColourRate ? formatSmallCurrency(model.extraLongColourRate) : '-' }}</td>
            <td class="text-center">{{ model.minimumsApplicable }}</td>
            <td class="text-center">{{ model.accessoryFeesIncludedInCPC }}</td>
            <td class="text-left">{{ model.baseBwVolume }}/{{model.baseColourVolume}}</td>
          </tr>
        </tbody>
      </v-table>

      <!-- Extra Long Life Note (Purple Box from Excel) -->
      <v-alert v-if="data.dsdTerritoryServiceRates.extraLongLifeNote" type="info" variant="tonal" class="mt-4 mb-3 text-caption" border="start" prominent>
        {{ data.dsdTerritoryServiceRates.extraLongLifeNote }}
      </v-alert>

      <!-- Additional Service Fees -->
      <v-card v-if="additionalServiceFees.length > 0" variant="outlined" class="mt-4">
        <v-card-title class="text-subtitle-2 font-weight-medium d-flex justify-space-between align-center">
          <span>Additional Service Fees</span>
          <v-btn color="primary" size="small" variant="text" prepend-icon="add" @click="addAdditionalServiceFee">Add Fee</v-btn>
        </v-card-title>
        <v-card-text class="pa-0">
          <v-table density="compact">
            <thead>
              <tr>
                <th>Description</th>
                <th class="text-right">Amount</th>
                <th class="text-center" style="width: 50px;"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(fee, index) in additionalServiceFees" :key="index">
                <td>
                  <v-text-field v-model="fee.description" density="compact" variant="plain" hide-details placeholder="Enter description"></v-text-field>
                </td>
                <td class="text-right">
                  <v-text-field v-model.number="fee.amount" type="number" density="compact" variant="plain" hide-details placeholder="0.00" min="0" step="0.01" @update:model-value="updateAdditionalServiceFeesTotal">
                    <template #prepend-inner><span class="text-medium-emphasis">$</span></template>
                  </v-text-field>
                </td>
                <td class="text-center">
                  <v-btn icon="delete" variant="text" density="comfortable" size="small" @click="removeAdditionalServiceFee(index)"></v-btn>
                </td>
              </tr>
              <tr>
                <td class="text-right font-weight-medium">Total:</td>
                <td class="text-right font-weight-medium">{{ formatCurrency(additionalServiceFeesTotal) }}</td>
                <td></td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>
      <div v-else class="mt-4">
          <v-btn color="primary" size="small" variant="text" prepend-icon="add" @click="addAdditionalServiceFee">Add Additional Service Fee</v-btn>
      </div>

      <!-- Colour Model Terms & Conditions -->
      <v-card variant="tonal" class="mt-4">
        <v-card-title class="text-subtitle-2 font-weight-medium">Colour Model Terms & Conditions</v-card-title>
        <v-card-text>
          <v-list density="compact" class="text-caption bg-transparent">
            <v-list-item v-for="(term, index) in data.colourModelTermsAndConditions" :key="index" class="pa-1">
              <template v-slot:prepend>
                <v-icon size="x-small" class="mr-2">circle</v-icon>
              </template>
              <v-list-item-title style="white-space: pre-line;">{{ term.text || term }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-card-text>
      </v-card>

      <!-- General Service Related Terms & Conditions -->
      <v-card variant="tonal" class="mt-4">
         <v-card-title class="text-subtitle-2 font-weight-medium">General Service Related Terms & Conditions</v-card-title>
        <v-card-text>
          <v-list density="compact" class="text-caption bg-transparent">
            <v-list-item v-for="(term, index) in data.generalServiceRelatedTermsAndConditions" :key="index" class="pa-1">
              <template v-slot:prepend>
                <v-icon size="x-small" class="mr-2">circle</v-icon>
              </template>
              <v-list-item-title style="white-space: pre-line;">{{ term.text || term }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-card-text>
      </v-card>

    </section-card>





     <!-- Lease Rate Factors -->
    <section-card title="Lease Rate Factors" class="mt-4">
      <v-table density="compact" class="mt-2 text-caption">
        <thead>
          <tr>
            <th class="text-left">LRF Type</th>
            <!-- Assuming data.leaseRateFactors.terms is an array like [{months: 36}, {months: 48}, ...] -->
            <th class="text-right" v-for="termHeader in data.leaseRateFactors.terms" :key="termHeader.months">{{ termHeader.months }} Months<br/>Equipment</th>
          </tr>
        </thead>
        <tbody>
          <!-- Assuming data.leaseRateFactors.rates is an array like [{type: 'LRF - quarterly', values: [{term: 36, rate: '0.00'}, ...]}, ...] -->
          <tr v-for="lrfRow in data.leaseRateFactors.rates" :key="lrfRow.type">
            <td class="text-left font-weight-bold">{{ lrfRow.type }}</td>
            <td class="text-right" v-for="termValue in lrfRow.values" :key="termValue.term">{{ termValue.rate }}</td>
          </tr>
        </tbody>
      </v-table>

      <v-textarea
        v-model="leaseRateFactorComments"
        label="Lease Rate Factors Comments"
        variant="outlined"
        rows="2"
        auto-grow
        class="mt-4"
      ></v-textarea>
    </section-card>

    <!-- Soft Costs -->
    <section-card title="Soft Costs" class="mt-4">
      <v-alert type="info" variant="tonal" class="mb-2 text-caption" border="start" v-if="data.softCosts.applicabilityNote">
        {{ data.softCosts.applicabilityNote }}
      </v-alert>
      <v-alert type="warning" variant="tonal" class="mb-3 text-caption" border="start" v-if="data.softCosts.deliveryFeesNote">
        {{ data.softCosts.deliveryFeesNote }}
      </v-alert>
      
      <v-table density="compact" class="mt-2 text-caption">
        <thead>
          <tr>
            <th class="text-left" style="width: 35%;">Description</th>
            <th v-for="(model, colIndex) in selectedModels" :key="colIndex" class="text-center">
              <v-select
                v-model="selectedModels[colIndex]"
                :items="getAvailableModels(colIndex)"
                label="Select Item"
                density="compact"
                variant="underlined"
                hide-details
                return-object
                item-title="label"
                item-value="value"
                @update:model-value="addNewColumnIfNeeded"
                class="model-selector"
              ></v-select>
            </th>
            <th v-if="selectedModels.length < modelOptions.length" class="text-center">
              <v-btn
                icon="mdi-plus"
                variant="text"
                density="comfortable"
                @click="addNewColumn"
                size="small"
              ></v-btn>
            </th>

          </tr>
        </thead>
        <tbody>
          <template v-for="(item, rowIndex) in softCostItems" :key="rowIndex">
            <tr>
              <td>
                <v-text-field
                  v-model="item.description"
                  density="compact"
                  variant="plain"
                  hide-details
                  placeholder="Enter description"
                ></v-text-field>
              </td>
              <td 
                v-for="(model, colIndex) in selectedModels" 
                :key="`${rowIndex}-${colIndex}`"
                class="text-center"
              >
                {{ model ? getCostForModel(item, model.value) : '' }}
              </td>
              <td v-if="selectedModels.length < modelOptions.length"></td>
            </tr>

          </template>
        </tbody>
      </v-table>
      <v-btn color="primary" size="small" variant="text" prepend-icon="add" @click="addSoftCostItem" class="mt-2">Add Soft Cost</v-btn>

        
      <!-- Additional Delivery Fees -->
      <v-card variant="outlined" class="mb-4">
        <v-card-title class="text-subtitle-2 font-weight-medium">Additional Delivery Fees</v-card-title>
        <v-card-text class="pa-0">
          <v-table density="compact">
            <thead>
              <tr>
                <th>Item</th>
                <th class="text-right">Cost</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="fee in data.additionalDeliveryFees.fees" :key="fee.item">
                <td>{{ fee.item }}</td>
                <td class="text-right">{{ formatCurrency(fee.cost) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>

      <!-- Other Fees -->
      <v-card variant="outlined" class="mb-4">
        <v-card-title class="text-subtitle-2 font-weight-medium d-flex justify-space-between align-center">
          <span>Other Fees</span>
          <v-btn
            color="primary"
            size="small"
            variant="text"
            prepend-icon="add"
            @click="addOtherFee"
          >
            Add Fee
          </v-btn>
        </v-card-title>
        <v-card-text class="pa-0">
          <v-table density="compact">
            <thead>
              <tr>
                <th>Description</th>
                <th class="text-right">Amount</th>
                <th class="text-center" style="width: 50px;"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(fee, index) in otherFees" :key="index">
                <td>
                  <v-text-field
                    v-model="fee.description"
                    density="compact"
                    variant="plain"
                    hide-details
                    placeholder="Enter description"
                  ></v-text-field>
                </td>
                <td class="text-right">
                  <v-text-field
                    v-model.number="fee.amount"
                    type="number"
                    density="compact"
                    variant="plain"
                    hide-details
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    @update:model-value="updateOtherFeesTotal"
                  >
                    <template #prepend-inner>
                      <span class="text-medium-emphasis">$</span>
                    </template>
                  </v-text-field>
                </td>
                <td class="text-center">
                  <v-btn
                    icon="delete"
                    variant="text"
                    density="comfortable"
                    size="small"
                    @click="removeOtherFee(index)"
                  ></v-btn>
                </td>
              </tr>
              <tr v-if="otherFees.length > 0">
                <td class="text-right font-weight-medium">Total:</td>
                <td class="text-right font-weight-medium">{{ formatCurrency(otherFeesTotal) }}</td>
                <td></td>
              </tr>
              <tr v-if="otherFees.length === 0">
                <td colspan="3" class="text-center text-medium-emphasis py-4">
                  No other fees added yet. Click 'Add Fee' to add one.
                </td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>

      <p v-if="data.softCosts.generalEquipmentNote" class="text-caption mt-2"><em>{{ data.softCosts.generalEquipmentNote }}</em></p>
      <div v-if="data.softCosts.detailedNotes && data.softCosts.detailedNotes.length > 0" class="mt-3">
        <p v-for="note in data.softCosts.detailedNotes" :key="note.noteId" class="text-caption">
          <strong>Note {{ note.noteId }}:</strong> {{ note.text }}
        </p>
      </div>

      <!-- General Notes and Conditions (Moved from Footer) -->
      <v-card variant="tonal" class="mt-4">
        <v-card-title class="text-subtitle-2 font-weight-medium">General Notes & Conditions</v-card-title>
        <v-card-text>
          <div v-for="(noteBlock, index) in data.footerNotes" :key="index" class="mb-3">
            <p v-if="noteBlock.title" class="text-subtitle-2 font-weight-bold">{{ noteBlock.title }}</p>
            <p class="text-caption" style="white-space: pre-line;">{{ noteBlock.text }}</p>
          </div>
        </v-card-text>
      </v-card>
    </section-card>
  </v-container>
</template>

<script setup>
import { ref, computed } from 'vue';
import { quoteData as initialData } from '../data/dummy-quote-data.js';

// Ensure dummy-quote-data.js has:
// data.dsdTerritoryServiceRates.extraLongLifeNote (string)
// data.colourModelTermsAndConditions (array of strings or objects with {text: string})
// data.generalServiceRelatedTermsAndConditions (array of strings or objects with {text: string})
// data.leaseRateFactors (object with terms array [{months: 36},...] and rates array of objects [{type: 'LRF - quarterly', values: [{term: 36, rate: '0.00'}, ...]}])
// data.softCosts (object with applicabilityNote, deliveryFeesNote, costItems array [{description, cost, noteRef}], generalEquipmentNote, detailedNotes array [{noteId, text}])
// data.additionalDeliveryFees (object with title and fees array [{item, cost}])
// data.footerNotes (array of objects with {title, text})
// data.leaseRateFactors (object with terms array and rates array of objects)
// data.softCosts (object with applicabilityNote, deliveryFeesNote, costItems array, generalEquipmentNote, detailedNotes array)
// data.additionalDeliveryFees (object with title and fees array)
// data.footerNotes (array of objects with title and text)
import SectionCard from '../components/SectionCard.vue';

console.log('QuotePage.vue: initialData loaded:', initialData);
console.log('QuotePage.vue: typeof initialData:', typeof initialData);
const data = ref(initialData);

// Editable comments/notes fields
const priceDeskComments = ref(initialData.keyInstructions?.map(i => i.text).join('\n') || '');
const serviceComments = ref(initialData.dsdTerritoryServiceRates.keyInstruction || '');
const leaseRateFactorComments = ref(''); // No initial data for this one

// Additional Service Fees
const additionalServiceFees = ref([]);
const additionalServiceFeesTotal = ref(0);

const addAdditionalServiceFee = () => {
  additionalServiceFees.value.push({ description: '', amount: 0 });
  updateAdditionalServiceFeesTotal();
};

const removeAdditionalServiceFee = (index) => {
  additionalServiceFees.value.splice(index, 1);
  updateAdditionalServiceFeesTotal();
};

const updateAdditionalServiceFeesTotal = () => {
  additionalServiceFeesTotal.value = additionalServiceFees.value.reduce((sum, fee) => {
    return sum + (parseFloat(fee.amount) || 0);
  }, 0);
};

// --- Utility Functions ---
const formatCurrency = (value) => {
  if (value == null || typeof value !== 'number') return 'N/A'; // Handles null, undefined, or non-numbers
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

const formatSmallCurrency = (value) => {
  if (value == null || typeof value !== 'number') return 'N/A'; // Handles null, undefined, or non-numbers
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 5, maximumFractionDigits: 5 }).format(value);
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

const onApprove = () => {
  // TODO: Implement quote approval logic
  console.log('Quote approved');
};

// Model selection for soft costs
const modelOptions = [
  { value: 'mfp-a3', label: 'MFP (A3)' },
  { value: 'mfp-bw-a4', label: 'MFP (B&W A4)' },
  { value: 'scanner', label: 'Scanner' },
  { value: 'printer', label: 'Printer' },
  { value: 'production', label: 'Production' }
];

// Track selected models for each column
const selectedModels = ref([null]); // Start with one empty column

// Add a new empty column
const addNewColumn = () => {
  if (selectedModels.value.length < modelOptions.length) {
    selectedModels.value.push(null);
  }
};

// Add new column automatically when a model is selected
const addNewColumnIfNeeded = () => {
  const hasEmptyColumn = selectedModels.value.some(item => item === null);
  const allModelsSelected = selectedModels.value.filter(Boolean).length === modelOptions.length;
  
  if (!hasEmptyColumn && !allModelsSelected) {
    addNewColumn();
  }
};

// Get available models that haven't been selected yet
const getAvailableModels = (currentIndex) => {
  const selectedValues = selectedModels.value
    .filter((_, index) => index !== currentIndex) // Exclude current selection
    .map(item => item?.value)
    .filter(Boolean);
    
  return modelOptions.filter(option => !selectedValues.includes(option.value));
};

// Soft Costs Management
const softCostItems = ref(
  JSON.parse(JSON.stringify(initialData.softCosts.costItems || []))
);

const addSoftCostItem = () => {
  softCostItems.value.push({ description: '', costs: {} });
};

const removeSoftCostItem = (index) => {
  softCostItems.value.splice(index, 1);
};

const softCostTableColspan = computed(() => {
  let cols = 1; // Description
  cols += selectedModels.value.length; // Models
  if (selectedModels.value.length < modelOptions.value.length) {
    cols++; // Add column button
  }
  return cols;
});

// Get cost for a specific model
const getCostForModel = (item, modelValue) => {
  if (!item) return '';
  
  // If item has a direct cost property (current structure)
  if (item.cost !== undefined) {
    return typeof item.cost === 'number' ? formatCurrency(item.cost) : item.cost;
  }
  
  // If item has model-specific costs (new structure)
  if (item.costs) {
    // If costs is a number, return it directly
    if (typeof item.costs === 'number') {
      return formatCurrency(item.costs);
    }
    
    // If costs is an object with model-specific pricing
    if (typeof item.costs === 'object' && modelValue) {
      const modelCost = item.costs[modelValue];
      return modelCost !== undefined ? formatCurrency(modelCost) : 'N/A';
    }
  }
  
  // Default fallback
  return item.cost || '';
};

// Other Fees
const otherFees = ref([
  // Initial empty fee entry
  { description: '', amount: 0 }
]);
const otherFeesTotal = ref(0);

// Add a new empty fee row
const addOtherFee = () => {
  otherFees.value.push({ description: '', amount: 0 });
};

// Remove a fee row
const removeOtherFee = (index) => {
  if (otherFees.value.length > 1) {
    otherFees.value.splice(index, 1);
    updateOtherFeesTotal();
  }
};

// Update the total when fees change
const updateOtherFeesTotal = () => {
  otherFeesTotal.value = otherFees.value.reduce((sum, fee) => {
    return sum + (parseFloat(fee.amount) || 0);
  }, 0);
};

const exportQuote = () => {
  // TODO: Include otherFees in export
  console.log('Exporting quote to Excel...');
  console.log('Other Fees:', otherFees.value);
};

</script>

<style scoped>
/* Add any specific styles here if needed, but try to use Vuetify classes first */
.text-caption {
  color: rgba(0,0,0,0.6);
}
/* Ensure section-card has a class or use a more specific selector if SectionCard.vue doesn't have global styles */
.v-card.my-4 { /* Targeting SectionCard via its root element and class */
  margin-bottom: 24px !important; /* Overriding Vuetify's default if needed, or use more specific classes */
}
</style>
