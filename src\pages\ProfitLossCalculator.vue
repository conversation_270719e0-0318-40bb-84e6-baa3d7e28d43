<template>
  <v-container fluid class="pnl-page">
    <!-- Header Section -->
    <v-row class="mb-4 header-section align-center">
      <v-col cols="12" md="4">
        <v-text-field
          v-model="customerName"
          label="Customer Name"
          variant="outlined"
          density="compact"
          hide-details
        ></v-text-field>
      </v-col>
      <v-col cols="12" md="2">
        <v-select
          v-model="category"
          :items="['Special SAS', 'Education', 'Government']"
          label="Category"
          variant="outlined"
          density="compact"
          hide-details
        ></v-select>
      </v-col>
      <v-col cols="12" md="6" class="text-right">
        <v-btn
          color="primary"
          prepend-icon="download"
          @click="exportToCSV"
        >
          Export to CSV
        </v-btn>
      </v-col>
    </v-row>

    <!-- Category Tabs -->
    <v-tabs v-model="activeTab" grow class="mb-4">
      <v-tab value="hardware">Hardware</v-tab>
      <v-tab value="solution">Solution</v-tab>
    </v-tabs>

    <v-window v-model="activeTab">
      <v-window-item :value="activeTab">

    <!-- Deal Summary -->
    <v-card class="mb-6 deal-summary-card" outlined>
      <v-card-title class="text-h6">Deal Summary</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total Quantity</div>
            <div class="value">{{ currentDealSummary.totalQuantity }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>MSRP</div>
            <div class="value">{{ formatCurrency(currentDealSummary.totalMSRP) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Requested Selling Price</div>
            <div class="value highlighted-summary-value">{{ formatCurrency(currentDealSummary.totalRequestedSellingPrice) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Percentage of MSRP</div>
            <div class="value">{{ currentDealSummary.percentageOfMSRP.toFixed(2) }}%</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Cost</div>
            <div class="value">{{ formatCurrency(currentDealSummary.totalProductCost) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total Cost</div>
            <div class="value highlighted-summary-value">{{ formatCurrency(currentDealSummary.overallTotalCost) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total CMAC Support Amount</div>
            <div class="value">{{ formatCurrency(currentDealSummary.grandTotalCmacSupportAmount) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total CMAC Cost</div>
            <div class="value">{{ formatCurrency(currentDealSummary.grandTotalCMACCost) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>GP %</div>
            <div class="value" :class="currentDealSummary.gpPercentage >= 0 ? 'text-green' : 'text-red'">{{ currentDealSummary.gpPercentage.toFixed(2) }}%</div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Global Controls -->
    <v-card class="mb-4" outlined>
      <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
         Adjustments
      </v-card-title>
      <v-card-text>
        <v-row dense align="center">
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model.number="globalDiscount"
              label="Discount"
              type="number"
              variant="outlined"
              density="compact"
              hide-details
              step="0.1"
              min="0"
              max="100"
              suffix="%"
              @update:model-value="debouncedApplyGlobalDiscount"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model.number="globalTargetGP"
              label="Target GP %"
              type="number"
              variant="outlined"
              density="compact"
              hide-details
              step="0.1"
              min="-100"
              max="100"
              suffix="%"
              @update:model-value="debouncedApplyGlobalTargetGP"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-btn
              color="secondary"
              variant="outlined"
              prepend-icon="refresh"
              @click="resetGlobalValues"
              block
            >
              Reset All
            </v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-btn
              color="primary"
              variant="outlined"
              prepend-icon="save"
              @click="saveChanges"
              block
            >
              Save Changes
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Product Groups -->
    <div v-for="(group, groupIndex) in currentGroups" :key="groupIndex" class="mb-6">
      <v-card outlined>
        <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
          {{ group.groupName }}
        </v-card-title>
        <div style="overflow-x: auto; width: 100%;">
          <v-table density="compact">
            <thead>
              <tr>
                <th>Product</th>
                <th>Item Number</th>
                <th>BSD Qty</th>
                <th>Dealer Its</th>
                <th>Total Qty</th>
                <th>MSRP</th>
                <th>Req. Selling Price (Unit)</th>
                <th>Total Req. Selling Price</th>
                <th>% of MSRP</th>
                <th>Cost (Unit)</th>
                <th>Total Cost</th>
                <th>CMAC Support (Unit)</th>
                <th class="support-percentage-column">Support %</th>
                <th>CMAC Cost (Unit)</th>
                <th>Total CMAC Cost</th>
                <th style="width: 95px; min-width: 95px;">GP %</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, itemIndex) in group.items" :key="`item-${itemIndex}`">
                <td>{{ item.product }}</td>
                <td>{{ item.itemNumber }}</td>
                <td>{{ item.bsdQuantity }}</td>
                <td>{{ item.dealerIts }}</td>
                <td>{{ (item.bsdQuantity || 0) + (item.dealerIts || 0) }}</td>
                <td>{{ formatCurrency(item.msrp) }}</td>
                <td>
                  <v-text-field
                    :class="validationClass(item, 'price')"
                    v-model.number="item.unitRequestedSellingPrice"
                    type="number"
                    variant="underlined"
                    density="compact"
                    hide-details
                    step="0.01"
                    min="0"
                    @update:model-value="updateSellingPrice(item)"
                  />
                </td>
                <td>{{ formatCurrency(item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                <td>{{ item.msrp && item.unitRequestedSellingPrice ? ((item.unitRequestedSellingPrice / item.msrp) * 100).toFixed(2) + '%' : 'N/A' }}</td>
                <td>{{ formatCurrency(item.unitCost) }}</td>
                <td>{{ formatCurrency(item.unitCost * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                <td>
                  <v-text-field 
                    :class="validationClass(item, 'supportValue')"
                    v-model.number="item.cmacSupportValue" 
                    type="number" 
                    variant="underlined" 
                    density="compact" 
                    hide-details
                    step="0.01"
                    min="0"
                    @update:model-value="updateFromSupportValue(item)"
                  />
                </td>
                <td class="support-percentage-column">
                  <v-text-field 
                    :class="validationClass(item, 'supportPercentage')"
                    v-model.number="item.supportPercentage" 
                    type="number" 
                    variant="underlined" 
                    density="compact" 
                    hide-details
                    step="0.01"
                    min="0"
                    max="100"
                    suffix="%"
                    @update:model-value="updateFromSupportPercentage(item)"
                  />
                </td>
                <td>{{ formatCurrency(item.unitCost - item.cmacSupportValue) }}</td>
                <td>{{ formatCurrency((item.unitCost - item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                <td :class="{'text-red': (((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / ((item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) || 1) * 100) < 0}">
                  <v-text-field
                    :class="validationClass(item, 'gp')"
                    v-model.number="item.gpPercentage"
                    type="number"
                    variant="underlined"
                    density="compact"
                    hide-details
                    step="0.01"
                    min="-100"
                    max="100"
                    suffix="%"
                    @update:model-value="updateFromGPPercentage(item)"
                  />
                </td>
              </tr>

            </tbody>
            <tfoot>
              <tr class="font-weight-bold group-totals-row">
                <td colspan="2">Group Totals:</td>
                <td>{{ getGroupTotals(group).totalBsdQuantity }}</td>
                <td>{{ getGroupTotals(group).totalDealerIts }}</td>
                <td>{{ getGroupTotals(group).totalQuantity }}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalMSRP) }}</td>
                <td>{{" "}}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalRequestedSellingPrice) }}</td>
                <td>{{ getGroupTotals(group).avgPercentageOfMSRP.toFixed(2) }}%</td>
                <td>{{" "}}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalItemCost) }}</td>
                <td>{{" "}}</td>
                <td>{{" "}}</td>
                <td>{{" "}}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalGroupCMACCost) }}</td>
                <td>{{ getGroupTotals(group).groupGPPercentage.toFixed(2) }}%</td>
              </tr>
            </tfoot>
          </v-table>
        </div>
      </v-card>

    </div>

    <!-- Final Summary Section -->
    <v-card class="mt-6 final-summary-card" outlined>
      <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
        Deal Cost Summary & GP
      </v-card-title>
      <v-list dense class="pa-0">
        <v-list-item>
          <v-row no-gutters>
            <v-col cols="8">Product Table Total CMAC Support Amount:</v-col>
            <v-col cols="4" class="text-right">{{ formatCurrency(currentDealSummary.grandTotalCmacSupportAmount) }}</v-col>
          </v-row>
        </v-list-item>
        <v-divider class="my-2"></v-divider>
        <v-list-item class="font-weight-bold">
          <v-row no-gutters>
            <v-col cols="8">Subtotal of Above Costs:</v-col>
            <v-col cols="4" class="text-right">{{ formatCurrency(currentDealSummary.subtotalOfAboveCosts) }}</v-col>
          </v-row>
        </v-list-item>
        <v-divider class="my-2"></v-divider>
        <v-list-item class="font-weight-bold success--text">
          <v-row no-gutters>
            <v-col cols="8">Overall GP %:</v-col>
            <v-col cols="4" class="text-right">{{ currentDealSummary.gpPercentage.toFixed(2) }}%</v-col>
          </v-row>
        </v-list-item>
      </v-list>
    </v-card>
      </v-window-item>
    </v-window>

  </v-container>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import type { ProfitLossItem } from '@/services/salesRequestService';
import { getProfitLossData } from '@/services/salesRequestService';
import { ref, computed, watch, onUnmounted, onMounted } from 'vue';
import { saveAs } from 'file-saver';
import { saveProfitLossChanges } from '@/services/salesRequestService';
import { debounce } from 'lodash';

interface PNLItem {
  requestItemId?: number;
  itemId?: number;
  product: string;
  itemNumber: string;
  bsdQuantity: number;
  dealerIts: number;
  msrp: number;
  unitRequestedSellingPrice: number;
  unitCost: number;
  cmacSupportValue: number;
  supportPercentage: number;
  gpPercentage: number;
  // Add any other fields that might be needed for calculations
}

interface ProductGroup {
  groupName: string;
  items: PNLItem[];
}

interface PNLData {
  customerName: string;
  categoryInfo: {
    category: string;
  };
  productGroups: ProductGroup[];
}

// --- Split data into Hardware vs Solution groups ---
const customerName = ref('');
const category = ref('');

const hardwareGroups = ref<ProductGroup[]>([]);
const solutionGroups = ref<ProductGroup[]>([]);

// Independent global controls per tab
const hardwareDiscount = ref(0);
const solutionDiscount = ref(0);
const hardwareTargetGP = ref(0);
const solutionTargetGP = ref(0);

// activeTab = 'hardware' | 'solution'
const activeTab = ref<'hardware' | 'solution'>('hardware');

// Convenience computed accessors for the currently visible tab
const currentGroups = computed<ProductGroup[]>(() =>
  activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value
);

const globalDiscount = computed<number>({
  get() {
    return activeTab.value === 'hardware' ? hardwareDiscount.value : solutionDiscount.value;
  },
  set(val: number) {
    if (activeTab.value === 'hardware') hardwareDiscount.value = val; else solutionDiscount.value = val;
  }
});

const globalTargetGP = computed<number>({
  get() {
    return activeTab.value === 'hardware' ? hardwareTargetGP.value : solutionTargetGP.value;
  },
  set(val: number) {
    if (activeTab.value === 'hardware') hardwareTargetGP.value = val; else solutionTargetGP.value = val;
  }
});

const route = useRoute();

// Categorize items into hardware vs solution groups
const categorizeGroups = (items: ProfitLossItem[]): { hardware: ProductGroup[]; solution: ProductGroup[] } => {
  const createMap = () => ({ } as Record<string, ProductGroup>);
  const hardwareMap = createMap();
  const solutionMap = createMap();

  items.forEach(item => {
    const isSolution = item.isSolution === 'Y';
    if(item.msrp == 0){return;}
    const map = isSolution ? solutionMap : hardwareMap;
    const groupKey = item.modelName || item.displayName || 'Others';
    if (!map[groupKey]) {
      map[groupKey] = { groupName: groupKey, items: [] } as ProductGroup;
    }

    const pnlItem: PNLItem = {
      product: item.displayName || item.modelName || '',
      itemNumber: item.itemNumber || '',
      bsdQuantity: item.dsdQuantity || 0,
      dealerIts: item.dealerIt || 0,
      msrp: item.msrp || 0,
      unitRequestedSellingPrice: (item.requestedSellingPricePL ?? item.requestedSellingPrice) || 0,
      unitCost: item.wholesaleCost || 0,
      cmacSupportValue: item.cmacPrice || 0,
      supportPercentage: item.cmacPercentage ?? (item.wholesaleCost ? Math.round(((item.cmacPrice || 0) / item.wholesaleCost) * 10000) / 100 : 0),
      gpPercentage: 0,
      requestItemId: item.requestItemId,
      itemId: item.itemId
    };

    if (!map[groupKey].items) map[groupKey].items = [];
    map[groupKey].items.push(pnlItem);
  });

  return { hardware: Object.values(hardwareMap), solution: Object.values(solutionMap) };
};



const saveChanges = async () => {
  const idParam = route.params.id;
  if (!idParam) return;
  const requestIdNum = Number(idParam);
  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  const payload = targetGroups.flatMap(g => g.items.map(it => ({
    requestItemId: it.requestItemId ?? 0,
    itemId: it.itemId ?? 0,
    requestId: requestIdNum,
    requestedSellingPricePL: it.unitRequestedSellingPrice,
    cmacPrice: it.cmacSupportValue
  })));
  try {
    await saveProfitLossChanges(requestIdNum, payload);
    // Optionally show success toast
  } catch (e) {
    console.error('Failed to save P&L changes', e);
  }
};

onMounted(async () => {
  const idParam = route.params.id;
  if (!idParam) return;
  try {
    const data = await getProfitLossData(Number(idParam));
    const { hardware, solution } = categorizeGroups(data);
    hardwareGroups.value = hardware;
    solutionGroups.value = solution;

    // Compute GP % for all items on initial load
    [hardwareGroups.value, solutionGroups.value].forEach(arr =>
      arr.forEach(group => group.items.forEach(it => recalculateItemDerived(it)))
    );
    // TODO set customerName and category if available in response
  } catch (error) {
    console.error('Failed to fetch P&L data', error);
  }
});

// Helper to round to two decimal places
const round2 = (val: number) => {
  if (typeof val !== 'number' || isNaN(val)) return 0;
  return Math.round((val + Number.EPSILON) * 100) / 100;
};

// Return 'text-red' when a value is invalid per business rules so UI can highlight it
const validationClass = (item: PNLItem, field: string) => {
  switch (field) {
    case 'price':
      return item.unitRequestedSellingPrice < 0 || (item.msrp && item.unitRequestedSellingPrice > item.msrp) ? 'text-red' : '';
    case 'supportValue':
      return item.cmacSupportValue < 0 || item.cmacSupportValue > item.unitCost ? 'text-red' : '';
    case 'supportPercentage':
      return item.supportPercentage < 0 || item.supportPercentage > 100 ? 'text-red' : '';
    case 'gp':
      return item.gpPercentage < 0 ? 'text-red' : '';
    default:
      return '';
  }
};

const formatCurrency = (value: number) => {
  if (typeof value !== 'number' || isNaN(value)) return '$0.00';
  return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
};

const getGroupTotals = (group: ProductGroup) => {
  let totalBsdQuantity = 0;
  let totalDealerIts = 0;
  let totalQuantity = 0;
  let totalMSRP = 0;
  let totalRequestedSellingPrice = 0;
  let totalItemCost = 0;
  let totalCmacSupportAmount = 0;
  let totalGroupCMACCost = 0;

  group.items.forEach(item => {
    const itemTotalQuantity = (item.bsdQuantity || 0) + (item.dealerIts || 0);
    totalBsdQuantity += (item.bsdQuantity || 0);
    totalDealerIts += (item.dealerIts || 0);
    totalQuantity += itemTotalQuantity;
    totalMSRP += item.msrp * itemTotalQuantity;
    totalRequestedSellingPrice += item.unitRequestedSellingPrice * itemTotalQuantity;
    totalItemCost += item.unitCost * itemTotalQuantity;
    totalCmacSupportAmount += item.cmacSupportValue * itemTotalQuantity;
    totalGroupCMACCost += (item.unitCost - item.cmacSupportValue) * itemTotalQuantity;
  });

  const groupGrossProfit = totalRequestedSellingPrice - totalItemCost + totalCmacSupportAmount;
  const groupGPPercentage = totalRequestedSellingPrice !== 0 ? (groupGrossProfit / totalRequestedSellingPrice) * 100 : 0;
  const avgPercentageOfMSRP = totalMSRP !== 0 ? (totalRequestedSellingPrice / totalMSRP) * 100 : 0;

  return {
    totalBsdQuantity,
    totalDealerIts,
    totalQuantity,
    totalMSRP,
    totalRequestedSellingPrice,
    avgPercentageOfMSRP,
    totalItemCost: totalItemCost,
    totalCmacSupportAmount,
    totalGroupCMACCost,
    totalGroupGP: groupGrossProfit,
    groupGPPercentage,
  };
};

const updateSellingPrice = (item: PNLItem) => {
  if (item.msrp && item.unitRequestedSellingPrice >= 0) {
    // Update CMAC support value based on the new selling price
    item.cmacSupportValue = Math.max(0, item.unitCost - item.unitRequestedSellingPrice);

    // Compute support % as portion of unitCost
    if (item.unitCost) {
      item.supportPercentage = (item.cmacSupportValue / item.unitCost) * 100;
    } else {
      item.supportPercentage = 0;
    }

    // round to 2 decimals
    item.unitRequestedSellingPrice = round2(item.unitRequestedSellingPrice);
    item.supportPercentage = round2(item.supportPercentage);
    item.cmacSupportValue = round2(item.cmacSupportValue);
    // update gp
    item.gpPercentage = item.unitCost ? round2(((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) / (item.unitCost - item.cmacSupportValue)) * 100) : 0;
  }
  recalculate();
};

const updateFromSupportValue = (item: PNLItem) => {
  if (item.cmacSupportValue >= 0) {
    // Calculate new selling price based on CMAC support
    item.unitRequestedSellingPrice = Math.max(0, item.unitCost - item.cmacSupportValue);

    // round values
    item.unitRequestedSellingPrice = round2(item.unitRequestedSellingPrice);
    item.cmacSupportValue = round2(item.cmacSupportValue);

    // Update support % based on unitCost
    if (item.unitCost) {
      item.supportPercentage = round2((item.cmacSupportValue / item.unitCost) * 100);
    } else {
      item.supportPercentage = 0;
    }
  }
  recalculate();
};

const updateFromGPPercentage = (item: PNLItem) => {
  // gpPercentage is markup on (unitCost - support)
  const marginBase = item.unitCost - item.cmacSupportValue;
  if (marginBase <= 0) return;
  const gpDecimal = item.gpPercentage / 100;
  const newSellingPrice = round2(marginBase * (1 + gpDecimal));
  item.unitRequestedSellingPrice = item.msrp ? Math.min(newSellingPrice, item.msrp) : newSellingPrice;
  // recalc other derived fields but do not alter support values
  recalculateItemDerived(item);
};

const recalculateItemDerived = (item: PNLItem) => {
  // Recalculate derived fields
  item.gpPercentage = item.unitCost ? round2(((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) / (item.unitCost - item.cmacSupportValue)) * 100) : 0;
};

const updateFromSupportPercentage = (item: PNLItem) => {
  if (item.supportPercentage >= 0 && item.supportPercentage <= 100) {
    // Calculate new CMAC support value based on support percentage (of unitCost)
    item.cmacSupportValue = round2(item.unitCost * (item.supportPercentage / 100));
    // Preserve existing GP %, adjust selling price to maintain it
    const gpDecimal = item.gpPercentage / 100;
    const newSellingPrice = round2((item.unitCost - item.cmacSupportValue) * (1 + gpDecimal));
    item.unitRequestedSellingPrice = item.msrp ? Math.min(newSellingPrice, item.msrp) : newSellingPrice;
  }
  recalculate();
};

// Global controls state



// Apply global discount to all products
const applyGlobalDiscount = () => {
  // Treat Global Discount as desired support %
  globalDiscount.value = round2(globalDiscount.value);

  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  targetGroups.forEach(group => {
    group.items.forEach(item => {
      item.supportPercentage = round2(globalDiscount.value);
      // Recalculate selling price to keep existing gp
      updateFromSupportPercentage(item);
    });
  });
};

// Apply target GP to all products
const applyGlobalTargetGP = () => {
  globalTargetGP.value = round2(globalTargetGP.value);

  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  targetGroups.forEach(group => {
    group.items.forEach(item => {
      if (!item.unitCost) return;

      if (!globalTargetGP.value) {
        // If Target GP blank/zero, reset selling price based on current % of MSRP
        const percentOfMSRP = item.msrp ? (item.unitRequestedSellingPrice / item.msrp) : 0;
        item.unitRequestedSellingPrice = round2(item.msrp * percentOfMSRP);
      } else {
        const targetGPDecimal = globalTargetGP.value / 100; // convert to decimal
        const newSellingPrice = (item.unitCost - item.cmacSupportValue) * (1 + targetGPDecimal);
        // Ensure it does not exceed MSRP
        item.unitRequestedSellingPrice = round2(Math.min(newSellingPrice, item.msrp));
      }
      // Update gpPercentage to reflect the new price
      item.gpPercentage = item.unitCost ? round2(((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) / (item.unitCost - item.cmacSupportValue)) * 100) : 0;
      // Do NOT touch cmacSupportValue or supportPercentage here
    });
  });
  recalculate();
};

// Debounced update functions
const debouncedApplyGlobalDiscount = debounce(applyGlobalDiscount, 300);
const debouncedApplyGlobalTargetGP = debounce(applyGlobalTargetGP, 300);

// Clean up debounce on component unmount
onUnmounted(() => {
  debouncedApplyGlobalDiscount.cancel();
  debouncedApplyGlobalTargetGP.cancel();
});

// Reset all global values
const resetGlobalValues = () => {
  globalDiscount.value = 0;
  globalTargetGP.value = 0;
  
  // Reset all products to their original MSRP
  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  targetGroups.forEach(group => {
    group.items.forEach(item => {
      if (item.msrp) {
        item.unitRequestedSellingPrice = item.msrp;
        updateSellingPrice(item);
      }
    });
  });
};

const recalculate = () => {
  // Any global recalculation logic can go here
  // Currently handled by computed properties and individual field updates
};

const exportToCSV = () => {
  // Create CSV header
  const headers = [
    'Group',
    'Product',
    'Item Number',
    'BSD Qty',
    'Dealer Its',
    'Total Qty',
    'MSRP',
    'Req. Selling Price (Unit)',
    'Total Req. Selling Price',
    '% of MSRP',
    'Cost (Unit)',
    'Total Cost',
    'CMAC Support (Unit)',
    'Support %',
    'CMAC Cost (Unit)',
    'Total CMAC Cost',
    'GP %'
  ];

  // Create CSV rows
  const rows = [...hardwareGroups.value, ...solutionGroups.value].flatMap(group => 
    group.items.map(item => ({
      group: group.groupName,
      product: item.product,
      itemNumber: item.itemNumber,
      bsdQty: item.bsdQuantity,
      dealerIts: item.dealerIts,
      totalQty: (item.bsdQuantity || 0) + (item.dealerIts || 0),
      msrp: item.msrp,
      unitRequestedSellingPrice: item.unitRequestedSellingPrice,
      totalRequestedSellingPrice: item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0)),
      percentOfMSRP: item.msrp && item.unitRequestedSellingPrice ? 
        (item.unitRequestedSellingPrice / item.msrp) * 100 : 0,
      unitCost: item.unitCost,
      totalCost: item.unitCost * ((item.bsdQuantity || 0) + (item.dealerIts || 0)),
      cmacSupport: item.cmacSupportValue,
      supportPercentage: item.supportPercentage,
      cmacCost: item.unitCost - item.cmacSupportValue,
      totalCmacCost: (item.unitCost - item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0)),
      gpPercentage: (item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) > 0 ? 
        (((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * 
        ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / 
        (item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0)))) * 100 : 0
    }))
  );

  // Convert to CSV
  const csvContent = [
    headers.join(','),
    ...rows.map(row => Object.values(row).map(value => 
      typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
    ).join(','))
  ].join('\r\n');

  // Create blob and download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, `P&L_Export_${new Date().toISOString().slice(0, 10)}.csv`);
};

// Reusable function to compute deal summary for any group list
const computeDealSummary = (groups: ProductGroup[]) => {
  let grandTotalBsdQuantity = 0;
  let grandTotalDealerIts = 0;
  let grandTotalQuantity = 0;
  let grandTotalMSRP = 0;
  let grandTotalRequestedSellingPrice = 0;
  let grandTotalProductCost = 0;
  let grandTotalCmacSupportAmount = 0;
  let grandTotalCMACCost = 0;

  groups.forEach(group => {
    const gTotals = getGroupTotals(group);
    grandTotalBsdQuantity += gTotals.totalBsdQuantity;
    grandTotalDealerIts += gTotals.totalDealerIts;
    grandTotalQuantity += gTotals.totalQuantity;
    grandTotalMSRP += gTotals.totalMSRP;
    grandTotalRequestedSellingPrice += gTotals.totalRequestedSellingPrice;
    grandTotalProductCost += gTotals.totalItemCost;
    grandTotalCmacSupportAmount += gTotals.totalCmacSupportAmount;
    grandTotalCMACCost += gTotals.totalGroupCMACCost;
  });

  const overallTotalCost = grandTotalProductCost - grandTotalCmacSupportAmount;
  const gpAmount = grandTotalRequestedSellingPrice - overallTotalCost;
  const gpPercentage = grandTotalRequestedSellingPrice !== 0 ? (gpAmount / grandTotalRequestedSellingPrice) * 100 : 0;
  const percentageOfMSRP = grandTotalMSRP !== 0 ? (grandTotalRequestedSellingPrice / grandTotalMSRP) * 100 : 0;

  return {
    totalBsdQuantity: grandTotalBsdQuantity,
    totalDealerIts: grandTotalDealerIts,
    totalQuantity: grandTotalQuantity,
    totalMSRP: grandTotalMSRP,
    totalRequestedSellingPrice: grandTotalRequestedSellingPrice,
    percentageOfMSRP,
    totalProductCost: grandTotalProductCost,
    grandTotalCMACCost,
    grandTotalCmacSupportAmount,
    overallTotalCost,
    gpPercentage,
    subtotalOfAboveCosts: grandTotalCmacSupportAmount,
  };
};

const currentDealSummaryHardware = computed(() => computeDealSummary(hardwareGroups.value));
const currentDealSummarySolution = computed(() => computeDealSummary(solutionGroups.value));
const currentDealSummary = computed(() =>
  activeTab.value === 'hardware' ? currentDealSummaryHardware.value : currentDealSummarySolution.value
);

// ... (rest of the script remains the same)
</script>

<style scoped>
.pnl-page {
  font-family: Arial, sans-serif;
  font-size: 0.9rem;
}

.header-section .v-input {
  margin-bottom: 0;
}

.deal-summary-card .v-col > div:first-child {
  font-weight: bold;
  color: #555;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.deal-summary-card .value {
  font-size: 1rem;
  font-weight: 500;
}

.deal-summary-card .value {
  font-weight: bold;
  font-size: 1.1em;
}

.deal-summary-card .highlighted-summary-value {
  background-color: #FFF9C4;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #FBC02D;
  display: inline-block;
}

.v-table {
  font-size: 0.85rem;
}

.text-red {
  color: red;
}

.text-green {
  color: green;
}

.group-totals-row td {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center; /* Ensure group totals are centered */
}

.final-summary-card .v-list-item {
  min-height: 36px;
}

.support-percentage-column {
  min-width: 90px; /* Adjust as needed */
}

.v-table thead th {
  white-space: nowrap;
}

.final-summary-card .v-divider {
  border-color: rgba(0, 0, 0, 0.12);
}

.id-commission-card,
.product-group-card {
  border: 1px solid #e0e0e0;
}

.additional-cost-row td,
.id-commission-row td {
  font-style: italic;
  background-color: #f9f9f9;
}

.v-table thead th,
.v-table tbody td {
  text-align: center;
}

.italic-description input,
.italic-description .v-label {
  font-style: italic !important;
}

.highlighted-summary-value {
  background-color: #FFF9C4;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #FBC02D;
  display: inline-block;
}

.additional-cost-row .italic-description,
.id-commission-row .italic-description {
  padding-left: 8px;
}

.additional-cost-row td:nth-child(2),
.id-commission-row td:nth-child(2) {
  font-size: 0.8em;
  color: #555;
  text-align: center;
}
/* Hide spinners from number input fields */
:deep(input[type=number])::-webkit-outer-spin-button,
:deep(input[type=number])::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

:deep(input[type=number]) {
  -moz-appearance: textfield; /* Firefox */
}
</style>
