<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="text-h5 py-4 bg-grey-lighten-4">
        <span>VP Rate Table</span>
      </v-card-title>

      <!-- Filter Section -->
      <v-card-text class="pb-0">
        <v-row class="mb-4 mt-4">
          <v-col cols="12" md="3">
            <v-select
              v-model="selectedMainframeUnit"
              :items="mainframeUnitOptions"
              item-title="title"
              item-value="value"
              label="Mainframe Unit"
              clearable
              variant="outlined"
              density="compact"
              :loading="loadingDropdowns"
              :disabled="loadingDropdowns"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="selectedServicePackId"
              :items="servicePackOptions.map(el=>({title:el.title.split(' ')[0], value:el.value}))"
              item-title="title"
              item-value="value"
              label="Service Pack"
              clearable
              variant="outlined"
              density="compact"
              :loading="loadingDropdowns"
              :disabled="loadingDropdowns"
            ></v-select>
          </v-col>

          <v-col cols="12" md="3">
            <v-select
              v-model="selectedPortfolioId"
              :items="portfolioOptions"
              item-title="title"
              item-value="value"
              label="Portfolio"
              clearable
              variant="outlined"
              density="compact"
              :loading="loadingDropdowns"
              :disabled="loadingDropdowns"
            ></v-select>
          </v-col>

          <!-- Accessory Inc.(CPC) Checkbox -->
          <v-col cols="12" md="3">
            <v-checkbox
              v-model="selectedAccessoryInc"
              label="Accessory Inc.(CPC)"
              :true-value="true"
              :false-value="false"
              density="compact"
              :disabled="loadingDropdowns"
            ></v-checkbox>
          </v-col>

          <v-col cols="12" md="3" >
            <v-btn
              color="primary"
              @click="searchVPRates"
              :loading="loading"
              :disabled="loading"
              class="mr-2"
              variant="elevated"
            >
              <v-icon left>search</v-icon>
              Search
            </v-btn>

            <v-btn
              color="secondary"
              @click="clearFilters"
              :disabled="loading"
              variant="outlined"
            >
              <v-icon left>filter_list_off</v-icon>
              Clear
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-text>
        <!-- Loading State -->
        <div v-if="loading" class="text-center pa-8">
          <!-- <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular> -->
          <!-- <div class="mt-4 text-h6">Loading VP Rate data...</div> -->
        </div>

        <!-- Error State -->
        <v-alert v-else-if="error" type="error" class="ma-4">
          <v-alert-title>Error Loading Data</v-alert-title>
          {{ error }}
          <template v-slot:append>
            <v-btn color="white" variant="text" @click="fetchVPRates">
              <v-icon>refresh</v-icon>
              Retry
            </v-btn>
          </template>
        </v-alert>

        <!-- Data Table -->
        <v-data-table
          v-else
          :headers="headers"
          :items="vpRates"
          :loading="loading"
          item-value="id"
          class="elevation-1"
          :items-per-page="25"
          :items-per-page-options="[10, 25, 50, 100]"
        >
          <!-- Custom header styling -->
          <template v-slot:headers="{ columns }">
            <tr>
              <template v-for="column in columns" :key="column.key">
                <td class="text-subtitle-2 font-weight-bold bg-grey-lighten-3">
                  {{ column.title }}
                </td>
              </template>
            </tr>
          </template>

          <!-- Custom cell formatting -->
          <template v-slot:item.rate="{ item }">
            <span class="font-weight-medium">
              {{ formatCurrency(item.rate) }}
            </span>
          </template>

          <template v-slot:item.createdAt="{ item }">
            {{ formatDate(item.createdAt) }}
          </template>

          <template v-slot:item.updatedAt="{ item }">
            {{ formatDate(item.updatedAt) }}
          </template>

          <template v-slot:item.isDealerAcceptedSr="{ item }">
            {{ item.isDealerAcceptedSr === 'Y' ? 'Yes' : 'No' }}
          </template>

          <!-- Actions column with 3-dots menu -->
          <template v-slot:item.actions="{ item }">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn
                  icon="more_vert"
                  size="small"
                  variant="text"
                  v-bind="props"
                  :title="'Actions'"
                >
                </v-btn>
              </template>
              <v-list density="compact">
                <!-- <v-list-item
                  @click="editItem(item)"
                  prepend-icon="edit"
                  title="Edit"
                >
                </v-list-item> -->
                <v-list-item
                  @click="viewDetails(item)"
                  prepend-icon="visibility"
                  title="View Details"
                >
                </v-list-item>
                <v-list-item
                  @click="openChargesDialog(item)"
                  prepend-icon="attach_money"
                  title="Add / Edit Charges"
                >
                </v-list-item>
              </v-list>
            </v-menu>
          </template>

          <!-- No data state -->
          <!-- <template v-slot:no-data>
            <div class="text-center pa-8">
              <v-icon size="64" color="grey-lighten-1">mdi-table-off</v-icon>
              <div class="text-h6 mt-4 text-grey-darken-1">No VP Rate data available</div>
              <v-btn color="primary" class="mt-4" @click="fetchVPRates">
                <v-icon left>refresh</v-icon>
                Refresh Data
              </v-btn>
            </div>
          </template> -->
        </v-data-table>
      </v-card-text>
    </v-card>

    <!-- Hardware Charges Dialog -->
    <HardwareChargesDialog
      v-model="showChargesDialog"
      :item="chargesItem"
      @saved="handleChargesSaved"
    />
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getVPRates, getWorksheetData, saveVPRate } from '@/lib/api';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import HardwareChargesDialog from './worksheets/HardwareChargesDialog.vue';

const appStore = useAppStore();
const snackbarStore = useSnackbarStore();

// Reactive data
const loading = ref(false);
const error = ref('');
const vpRates = ref<any[]>([]);

// Filter dropdown values
const selectedMainframeUnit = ref<number | null>(null);
const selectedServicePackId = ref<number | null>(null);
const selectedPortfolioId = ref<number | null>(null);
// Accessory Include (CPC) checkbox state (false -> 'N', true -> 'Y')
const selectedAccessoryInc = ref<boolean>(false);

// Dropdown options
const mainframeUnitOptions = ref<{ title: string; value: number }[]>([]);
const servicePackOptions = ref<{ title: string; value: number }[]>([]);
const portfolioOptions = ref<{ title: string; value: number }[]>([]);

// Loading states for dropdowns
const loadingDropdowns = ref(false);

// Dialog state for VP Rate editing (if still needed)
const showEditDialog = ref(false);
const editingItem = ref<any>(null);
const savingRate = ref(false);

// Hardware Charges dialog state
const showChargesDialog = ref(false);
const chargesItem = ref<any>(null);

// Table headers - adjust these based on your actual API response structure
const headers = ref([
  { title: 'ID', key: 'itemId', sortable: true },
  { title: 'Item Name', key: 'displayName', sortable: true },
  { title: 'Portfolio', key: 'portfolioValue', sortable: true },
  { title: 'Service Pack', key: 'servicePackValue', sortable: true },
  { title: 'Black & White', key: 'blackAndWhite', sortable: true },
  { title: 'Color', key: 'colorValue', sortable: true },
  { title: 'Over Size', key: 'oversizedValue', sortable: true },
  { title: 'Accessory Inc.(CPC)', key: 'isDealerAcceptedSr', sortable: true },
  // { title: 'Created Date', key: 'createdAt', sortable: true },
  // { title: 'Updated Date', key: 'updatedAt', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false, width: '100px' }
]);

// Fetch VP Rate data with optional filters
const fetchVPRates = async () => {
  try {
    loading.value = true;
    error.value = '';

    // Build query parameters
    const params: any = {};
    if (selectedMainframeUnit.value) params.itemId = selectedMainframeUnit.value;
    if (selectedServicePackId.value) params.servicePackId = selectedServicePackId.value;
    if (selectedPortfolioId.value) params.portfolioId = selectedPortfolioId.value;
    // Add checkbox value as parameter (Y when checked, N when unchecked)
    params.isDealerAcceptedSr = selectedAccessoryInc.value ? 'Y' : 'N';

    const response = await getVPRates(params);
    vpRates.value = [response.data] || [];

    if (vpRates.value.length === 0) {
      snackbarStore.show({
        text: 'No VP Rate data found',
        color: 'info',
        icon: 'info',
        timeout: 3000
      });
    }

  } catch (err: any) {
    console.error('Error fetching VP Rate data:', err);
    error.value = err.response?.data?.message || err.message || 'Failed to load VP Rate data';

    snackbarStore.show({
      text: 'Failed to load VP Rate data',
      color: 'error',
      icon: 'alert',
      timeout: 5000
    });
  } finally {
    loading.value = false;
  }
};

// Utility functions
const formatCurrency = (value: number | null | undefined): string => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { 
    style: 'currency', 
    currency: 'USD' 
  }).format(value);
};

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return 'Invalid Date';
  }
};

const viewDetails = (item: any) => {
  // Implement view details functionality
  console.log('View details for:', item);
  snackbarStore.show({
    text: `Viewing details for ${item.itemName || 'item'}`,
    color: 'info',
    icon: 'eye',
    timeout: 2000
  });
};

// Edit item function
const editItem = (item: any) => {
  editingItem.value = { ...item }; // Create a copy to avoid direct mutation
  showEditDialog.value = true;
};

// Open Hardware Charges dialog
const openChargesDialog = (item: any) => {
  chargesItem.value = { ...item };
  showChargesDialog.value = true;
};


// Handle save event from HardwareChargesDialog
const handleChargesSaved = async () => {
  // Refresh VP rate list after saving charges
  await fetchVPRates();
  snackbarStore.show({
    text: 'Charges saved successfully',
    color: 'success',
    icon: 'check',
    timeout: 3000
  });
};


// Handle dialog result from HardwareChargesDialog
const handleDialogResult = async (result: any) => {
  if (result && result.confirmed) {
    try {
      savingRate.value = true;

      // Prepare payload for API
      const payload = {
        ...editingItem.value,
        ...result.data
      };

      await saveVPRate(payload);

      snackbarStore.show({
        text: 'VP Rate updated successfully',
        color: 'success',
        icon: 'check',
        timeout: 3000
      });

      // Refresh the table data
      await fetchVPRates();

    } catch (error: any) {
      console.error('Error saving VP Rate:', error);
      snackbarStore.show({
        text: error.response?.data?.message || 'Failed to save VP Rate',
        color: 'error',
        icon: 'alert',
        timeout: 5000
      });
    } finally {
      savingRate.value = false;
    }
  }

  showEditDialog.value = false;
  editingItem.value = null;
};

// Load dropdown options
const loadDropdownOptions = async () => {
  try {
    loadingDropdowns.value = true;

    // Load all dropdown options concurrently
    const [portfolioLovs, servicePackLovs, worksheetResponse] = await Promise.all([
      getLov(LovCategories.PORTFOLIO),
      getLov(LovCategories.SERVICE_VALUE_PACK),
      getWorksheetData()
    ]);

    // Map portfolio options
    portfolioOptions.value = portfolioLovs.map((item: LovItem) => ({
      title: item.description,
      value: Number(item.lookupCode)
    }));

    // Map service pack options
    servicePackOptions.value = servicePackLovs.map((item: LovItem) => ({
      title: item.description,
      value: Number(item.lookupCode)
    }));

    // Map mainframe unit options from worksheet data
    mainframeUnitOptions.value = (worksheetResponse.data || []).map((item: any) => ({
      title: item.displayName || item.name || `Item ${item.itemId}`,
      value: Number(item.itemId)
    }));

  } catch (err: any) {
    console.error('Error loading dropdown options:', err);
    snackbarStore.show({
      text: 'Failed to load filter options',
      color: 'error',
      icon: 'alert',
      timeout: 5000
    });
  } finally {
    loadingDropdowns.value = false;
  }
};

// Search function to trigger filtered fetch
const searchVPRates = () => {
  if (!selectedMainframeUnit.value || !selectedServicePackId.value || !selectedPortfolioId.value) {
    snackbarStore.show({
      text: 'Please select all mandatory fields',
      color: 'warning',
      icon: 'warning',
      timeout: 3000
    });
    return;
  }
  fetchVPRates();
};

// Clear filters function
const clearFilters = () => {
  selectedMainframeUnit.value = null;
  selectedServicePackId.value = null;
  selectedPortfolioId.value = null;
  selectedAccessoryInc.value = false;
  fetchVPRates();
};

// Lifecycle
onMounted(async () => {
  await loadDropdownOptions();
  // fetchVPRates();
  appStore.stopPageLoader();
});
</script>

<style scoped>
.v-data-table {
  border-radius: 8px;
}

.v-data-table th {
  background-color: #f5f5f5 !important;
  font-weight: 600 !important;
}

.v-card-title {
  border-bottom: 1px solid #e0e0e0;
}
</style>
