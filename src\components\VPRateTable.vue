<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="text-h5 py-4 bg-grey-lighten-4">
        <span>VP Rate Table</span>
      </v-card-title>
      
      <v-card-text>
        <!-- Loading State -->
        <div v-if="loading" class="text-center pa-8">
          <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
          <div class="mt-4 text-h6">Loading VP Rate data...</div>
        </div>

        <!-- Error State -->
        <v-alert v-else-if="error" type="error" class="ma-4">
          <v-alert-title>Error Loading Data</v-alert-title>
          {{ error }}
          <template v-slot:append>
            <v-btn color="white" variant="text" @click="fetchVPRates">
              <v-icon>mdi-refresh</v-icon>
              Retry
            </v-btn>
          </template>
        </v-alert>

        <!-- Data Table -->
        <v-data-table
          v-else
          :headers="headers"
          :items="vpRates"
          :loading="loading"
          item-value="id"
          class="elevation-1"
          :items-per-page="25"
          :items-per-page-options="[10, 25, 50, 100]"
        >
          <!-- Custom header styling -->
          <template v-slot:headers="{ columns }">
            <tr>
              <template v-for="column in columns" :key="column.key">
                <td class="text-subtitle-2 font-weight-bold bg-grey-lighten-3">
                  {{ column.title }}
                </td>
              </template>
            </tr>
          </template>

          <!-- Custom cell formatting -->
          <template v-slot:item.rate="{ item }">
            <span class="font-weight-medium">
              {{ formatCurrency(item.rate) }}
            </span>
          </template>

          <template v-slot:item.createdAt="{ item }">
            {{ formatDate(item.createdAt) }}
          </template>

          <template v-slot:item.updatedAt="{ item }">
            {{ formatDate(item.updatedAt) }}
          </template>

          <!-- Actions column if needed -->
          <template v-slot:item.actions="{ item }">
            <v-btn
              icon="mdi-eye"
              size="small"
              variant="text"
              @click="viewDetails(item)"
              :title="`View details for ${item.name || 'item'}`"
            >
            </v-btn>
          </template>

          <!-- No data state -->
          <template v-slot:no-data>
            <div class="text-center pa-8">
              <v-icon size="64" color="grey-lighten-1">mdi-table-off</v-icon>
              <div class="text-h6 mt-4 text-grey-darken-1">No VP Rate data available</div>
              <v-btn color="primary" class="mt-4" @click="fetchVPRates">
                <v-icon left>mdi-refresh</v-icon>
                Refresh Data
              </v-btn>
            </div>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getVPRates } from '@/lib/api';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';

const appStore = useAppStore();
const snackbarStore = useSnackbarStore();

// Reactive data
const loading = ref(false);
const error = ref('');
const vpRates = ref<any[]>([]);

// Table headers - adjust these based on your actual API response structure
const headers = ref([
  { title: 'ID', key: 'id', sortable: true },
  { title: 'Item Name', key: 'itemName', sortable: true },
  { title: 'Portfolio', key: 'portfolio', sortable: true },
  { title: 'Service Pack', key: 'servicePack', sortable: true },
  { title: 'Rate', key: 'rate', sortable: true },
  { title: 'Created Date', key: 'createdAt', sortable: true },
  { title: 'Updated Date', key: 'updatedAt', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false, width: '100px' }
]);

// Fetch VP Rate data
const fetchVPRates = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await getVPRates();
    vpRates.value = response.data || [];
    
    if (vpRates.value.length === 0) {
      snackbarStore.show({
        text: 'No VP Rate data found',
        color: 'info',
        icon: 'info',
        timeout: 3000
      });
    }
    
  } catch (err: any) {
    console.error('Error fetching VP Rate data:', err);
    error.value = err.response?.data?.message || err.message || 'Failed to load VP Rate data';
    
    snackbarStore.show({
      text: 'Failed to load VP Rate data',
      color: 'error',
      icon: 'alert',
      timeout: 5000
    });
  } finally {
    loading.value = false;
  }
};

// Utility functions
const formatCurrency = (value: number | null | undefined): string => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { 
    style: 'currency', 
    currency: 'USD' 
  }).format(value);
};

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return 'Invalid Date';
  }
};

const viewDetails = (item: any) => {
  // Implement view details functionality
  console.log('View details for:', item);
  snackbarStore.show({
    text: `Viewing details for ${item.itemName || 'item'}`,
    color: 'info',
    icon: 'eye',
    timeout: 2000
  });
};

// Lifecycle
onMounted(() => {
  fetchVPRates();
  appStore.stopPageLoader();
});
</script>

<style scoped>
.v-data-table {
  border-radius: 8px;
}

.v-data-table th {
  background-color: #f5f5f5 !important;
  font-weight: 600 !important;
}

.v-card-title {
  border-bottom: 1px solid #e0e0e0;
}
</style>
