<script setup lang="ts">
/**
 * @file Worksheets tab component for Sales Request Form.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import ProductCategoryCondensedView from './ProductCategoryCondensedView.vue';
import ProductCategoryExpandedView from './ProductCategoryExpandedView.vue';
// Catalog service helpers
import {
  getHardwareMainUnits,
  getAccessories,
  getSolutionMainUnits,
  getSolutionSubItems,
  type CatalogItem
} from '@/services/salesRequestService';

// Props from parent (locations & customerId)
const wsProps = defineProps<{
  locations: any[];
  customerId: number | null;
}>();

// Add language support
const { t } = useI18n();

// -------------------------------- Lifecycle --------------------------------
onMounted(async () => {
  try {
    // Fetch main units in parallel
    const [hw, sol] = await Promise.all([getHardwareMainUnits(), getSolutionMainUnits()]);
    hardwareMainUnits.value = hw;
    solutionMainUnits.value = sol;

    // Build lookup map for itemId -> details
    const details: Record<string, { itemNumber: number | null; msrp: number | null }> = {};
    [...hw, ...sol].forEach((itm) => {
      details[String(itm.itemId)] = { itemNumber: itm.itemId, msrp: itm.msrp };
    });
    productDetails.value = details;
  } catch (e) {
    console.error('Failed to load catalog', e);
  }
});

// Tab state
const currentTab = ref('hardware'); // 'all', 'hardware', 'solutions'

// Dynamic catalog state ----------------------------------------------
// Map of itemId -> { itemNumber, msrp }
const productDetails = ref<Record<string, { itemNumber: number | null; msrp: number | null }>>({});

// Main-unit data
const hardwareMainUnits = ref<CatalogItem[]>([]);
const solutionMainUnits = ref<CatalogItem[]>([]);

// Child-item caches (accessories & sub-solution items) keyed by parentId
const accessoriesMap = ref<Record<number, CatalogItem[]>>({});
const solutionSubMap = ref<Record<number, CatalogItem[]>>({});

// Dropdown options (computed from dynamic refs)
const hardwareOptions = computed(() =>
  hardwareMainUnits.value.map((itm) => ({ title: itm.displayName, value: String(itm.itemId) }))
);

const softwareOptions = computed(() =>
  solutionMainUnits.value.map((itm) => ({ title: itm.displayName, value: String(itm.itemId) }))
);

// Separate form data for hardware and software
const hardwareProducts = ref<ProductItem[]>([createEmptyProduct()]);
const softwareProducts = ref<ProductItem[]>([]);

// Fetch (and cache) child items, then return dropdown list
const getSubProductOptions = (parentValue: string): { title: string; value: string }[] => {
  const parentId = Number(parentValue);
  if (isNaN(parentId)) return [];

  // Check hardware accessory map first
  if (accessoriesMap.value[parentId]) {
    return accessoriesMap.value[parentId].map((i) => ({ title: i.displayName, value: String(i.itemId) }));
  }
  // If not found, maybe it's a solution parent
  if (solutionSubMap.value[parentId]) {
    return solutionSubMap.value[parentId].map((i) => ({ title: i.displayName, value: String(i.itemId) }));
  }

  // If we don't have it cached, fetch in background
  if (!accessoriesMap.value[parentId] && !solutionSubMap.value[parentId]) {
    const parentHardware = hardwareMainUnits.value.find((h) => h.itemId === parentId);
    const fetcher = parentHardware ? getAccessories : getSolutionSubItems;
    fetcher(parentId)
      .then((items) => {
        if (parentHardware) accessoriesMap.value[parentId] = items;
        else solutionSubMap.value[parentId] = items;

        // add to global productDetails map
        const details = { ...productDetails.value };
        items.forEach((itm) => {
          details[String(itm.itemId)] = { itemNumber: itm.itemId, msrp: itm.msrp };
        });
        productDetails.value = details;
      })
      .catch((e) => console.error('Failed to load child items', e));
  }

  const list: CatalogItem[] = accessoriesMap.value[parentId] || solutionSubMap.value[parentId] || [];
  return list.map((i) => ({ title: i.displayName, value: String(i.itemId) }));
};

// Define the type for a product item
interface ProductField {
  type: string; // e.g., 'dropdown', 'number', 'text'
  value: string | number;
  readonly?: boolean;
  min?: number;
  max?: number;
}

interface ProductItem {
  location: { type: 'location'; value: any | null };
  requestItemId?: number; // existing item identifier from backend
  proposalType: ProductField; // only for hardware main units
  productName: ProductField;       // Main product or sub-product name/selection
  dsdQuantity: ProductField;       // DSD Quantity
  dealerITsQuantity: ProductField; // Dealer ITs Quantity
  itemNumber: ProductField;        // Item Number (often readonly, derived)
  requestSellingPrice: ProductField; // Requested Selling Price (often readonly, calculated)
  msrp: ProductField;              // Manufacturer's Suggested Retail Price (readonly, derived)
  percentOfMsrp: ProductField;     // Percentage of MSRP (user input, affects selling price)
  subProducts: ProductItem[];      // Array of accessories or sub-components
  displayName?: string; // Optional display name (for preview)
}



// Helper function to calculate total quantity (DSD + Dealer ITs) for a product or sub-product
const calculateTotalQuantity = (product: ProductItem): number => {
  const dsdQty = Number(product.dsdQuantity.value) || 0;
  const dealerQty = Number(product.dealerITsQuantity.value) || 0;
  return dsdQty + dealerQty;
};

// Helper function to calculate sum for a list of products (either selling price or MSRP)
const calculateSumForProductList = (products: ProductItem[], priceField: 'requestSellingPrice' | 'msrp'): number => {
  const calculateProductTotal = (product: ProductItem): number => {
    const productQty = calculateTotalQuantity(product);
    const productPrice = Number(product[priceField].value) || 0;
    const productTotal = productQty * productPrice;

    const subProductsTotal = product.subProducts.reduce((sum: number, sub: ProductItem) => {
      const subQty = calculateTotalQuantity(sub);
      const subPrice = Number(sub[priceField].value) || 0;
      return sum + (subQty * subPrice);
    }, 0);

    return productTotal + subProductsTotal;
  };
  return products.reduce((sum, p) => sum + calculateProductTotal(p), 0);
};

// Hardware Summary Calculations
const hardwareTotalSellingPrice = computed(() => {
  return calculateSumForProductList(hardwareProducts.value, 'requestSellingPrice');
});

const hardwareTotalMSRP = computed(() => {
  return calculateSumForProductList(hardwareProducts.value, 'msrp');
});

const hardwareMsrpPercentage = computed(() => {
  if (hardwareTotalMSRP.value === 0) return 0;
  return (hardwareTotalSellingPrice.value / hardwareTotalMSRP.value) * 100;
});

// Software Summary Calculations
const softwareTotalSellingPrice = computed(() => {
  return calculateSumForProductList(softwareProducts.value, 'requestSellingPrice');
});

const softwareTotalMSRP = computed(() => {
  return calculateSumForProductList(softwareProducts.value, 'msrp');
});

const softwareMsrpPercentage = computed(() => {
  if (softwareTotalMSRP.value === 0) return 0;
  return (softwareTotalSellingPrice.value / softwareTotalMSRP.value) * 100;
});

// Overall Summary calculations
const totalItems = computed(() => {
  const hardwareCount = hardwareProducts.value.length + 
    hardwareProducts.value.reduce((sum, p) => sum + p.subProducts.length, 0);
  const softwareCount = softwareProducts.value.length + 
    softwareProducts.value.reduce((sum, p) => sum + p.subProducts.length, 0);
  return hardwareCount + softwareCount;
});

const totalSellingPrice = computed(() => {
  return hardwareTotalSellingPrice.value + softwareTotalSellingPrice.value;
});

const totalMSRP = computed(() => {
  return hardwareTotalMSRP.value + softwareTotalMSRP.value;
});

const msrpPercentage = computed(() => {
  if (totalMSRP.value === 0) return 0;
  return (totalSellingPrice.value / totalMSRP.value) * 100;
});

// Function to create an empty product
function createEmptyProduct(): ProductItem {
  return {
    requestItemId: undefined,
    productName: { type: 'dropdown', value: '' },
    dsdQuantity: { type: 'number', value: 0 },
    dealerITsQuantity: { type: 'number', value: 0 },
    itemNumber: { type: 'text', value: '', readonly: true },
    requestSellingPrice: { type: 'number', value: 0, readonly: true },
    msrp: { type: 'number', value: 0, readonly: true },
    percentOfMsrp: { type: 'number', value: 100, min: 1, max: 100 },
    proposalType: { type: 'radio', value: 'primary' }, // only for hardware rows
    subProducts: []
  };
}

// Function to create an empty sub-product
function createEmptySubProduct(): ProductItem {
  return {
    productName: { type: 'dropdown', value: '' },
    dsdQuantity: { type: 'number', value: 0 },
    dealerITsQuantity: { type: 'number', value: 0 },
    itemNumber: { type: 'text', value: '', readonly: true },
    requestSellingPrice: { type: 'number', value: 0, readonly: true },
    msrp: { type: 'number', value: 0, readonly: true },
    percentOfMsrp: { type: 'number', value: 100, min: 1, max: 100 },
    subProducts: []
  };
}

// Add a new product
const addProduct = (type: 'hardware' | 'software') => {
  if (type === 'hardware') {
    hardwareProducts.value.push(createEmptyProduct());
  } else {
    softwareProducts.value.push(createEmptyProduct());
  }
};

// Remove a product
const removeProduct = (type: 'hardware' | 'software', index: number) => {
  if (type === 'hardware') {
    hardwareProducts.value.splice(index, 1);
  } else {
    softwareProducts.value.splice(index, 1);
  }
};

// Add a sub-product
const addSubProduct = (type: 'hardware' | 'software', productIndex: number) => {
  const products = type === 'hardware' ? hardwareProducts : softwareProducts;
  products.value[productIndex].subProducts.push(createEmptySubProduct());
};

// Remove a sub-product
const removeSubProduct = (type: 'hardware' | 'software', productIndex: number, subProductIndex: number) => {
  const products = type === 'hardware' ? hardwareProducts : softwareProducts;
  products.value[productIndex].subProducts.splice(subProductIndex, 1);
};

// Update product details when product is selected
const updateProductDetails = (product: any, productValue: string, isSubProduct = false) => {
  if (!productValue) return;
  const details = productDetails.value[productValue];
  if (details) {
    product.itemNumber.value = details.itemNumber;
    product.msrp.value = details.msrp;
    // Calculate selling price based on MSRP and percentage
    updateSellingPrice(product);
  }
};

// Update selling price based on MSRP and percentage
const updateSellingPrice = (product: any) => {
  const msrp = Number(product.msrp.value);
  const percent = Number(product.percentOfMsrp.value) || 0;
  
  // Ensure percentage is between 1 and 100
  if (percent < 1) product.percentOfMsrp.value = 1;
  if (percent > 100) product.percentOfMsrp.value = 100;
  
  // Calculate selling price (MSRP * (percent/100))
  product.requestSellingPrice.value = (msrp * (percent / 100)).toFixed(2);
};

// Handle percentage change
const handlePercentChange = (product: any) => {
  updateSellingPrice(product);
};

// Calculate summary for a single product and its sub-products (for expanded view prop)
const calculateProductSummaryForExpandedView = (product: ProductItem): { totalMsrp: number; totalSellingPrice: number; avgPercent: string | number } => {
  let mainProductMsrp = 0;
  let mainProductSellingPrice = 0;
  const mainProductQty = calculateTotalQuantity(product);

  if (product.msrp && product.msrp.value) {
    mainProductMsrp = mainProductQty * Number(product.msrp.value);
  }
  if (product.requestSellingPrice && product.requestSellingPrice.value) {
    mainProductSellingPrice = mainProductQty * Number(product.requestSellingPrice.value);
  }

  const subProductsTotalMsrp = product.subProducts.reduce((sum, sub) => {
    const subQty = calculateTotalQuantity(sub);
    return sum + (subQty * (Number(sub.msrp.value) || 0));
  }, 0);

  const subProductsTotalSellingPrice = product.subProducts.reduce((sum, sub) => {
    const subQty = calculateTotalQuantity(sub);
    return sum + (subQty * (Number(sub.requestSellingPrice.value) || 0));
  }, 0);

  const totalMsrpValue = mainProductMsrp + subProductsTotalMsrp;
  const totalSellingPriceValue = mainProductSellingPrice + subProductsTotalSellingPrice;
  
  let avgPercent: string | number = '0.00'; // Default to 0.00 if no prices
  if (totalMsrpValue > 0) {
    avgPercent = ((totalSellingPriceValue / totalMsrpValue) * 100).toFixed(2);
  } else if (totalSellingPriceValue > 0) {
    avgPercent = 'N/A'; // MSRP is 0 but selling price is not
  }

  return {
    totalMsrp: totalMsrpValue,
    totalSellingPrice: totalSellingPriceValue,
    avgPercent,
  };
};

const setFormData = (requestItems: any[]) => {
    if (!requestItems || requestItems.length === 0) {
        hardwareProducts.value = [];
        softwareProducts.value = [];
        return;
    }

    const newHardware: ProductItem[] = [];
    const newSoftware: ProductItem[] = [];
    const mainMap: Record<number, ProductItem> = {}; // itemId -> main product
    

    
    // First pass – create main products (no parent)
    requestItems.filter(it => !it.parentItemId).forEach(item => {
        const product = createEmptyProduct();
        product.requestItemId = item.requestItemId;
        product.itemNumber.value = item.itemId;
        product.msrp.value = item.msrp ?? 0;
        product.requestSellingPrice.value = item.requestedSellingPrice ?? 0;
        product.dsdQuantity.value = item.dsdQuantity ?? 0;
        product.dealerITsQuantity.value = item.dealerIt ?? 0;
        product.productName.value = String(item.itemId); // matches our option value
        product.percentOfMsrp.value = item.msrp && item.msrp > 0 ? (item.requestedSellingPrice / item.msrp) * 100 : 100;
        product.displayName = item.displayName; // Optional display name (for preview)
        product.proposalType.value = item.isPrimaryProposal === 'Y' ? 'primary' : 'alternate';
           // Hardware or solution?
           if (item.isSolution === 'Y') {
            newSoftware.push(product);
        } else {
            newHardware.push(product);
        }
      
        mainMap[item.itemId] = product;
    });
    // Second pass – sub products (have parentItemId)
    requestItems.filter(it => it.parentItemId).forEach(item => {
        const parent = mainMap[item.parentItemId];
        if (!parent) return; // skip if parent missing
        const sub = createEmptyProduct();
        sub.requestItemId = item.requestItemId;
        sub.itemNumber.value = item.itemId;
        sub.msrp.value = item.msrp ?? 0;
        sub.requestSellingPrice.value = item.requestedSellingPrice ?? 0;
        sub.dsdQuantity.value = item.dsdQuantity ?? 0;
        sub.dealerITsQuantity.value = item.dealerIt ?? 0;
        sub.productName.value = String(item.itemId);
        sub.displayName = item.displayName; // Optional display name (for preview)

        sub.percentOfMsrp.value = item.msrp && item.msrp > 0 ? (item.requestedSellingPrice / item.msrp) * 100 : 100;
        parent.subProducts.push(sub);
    });

        

        hardwareProducts.value = newHardware.length > 0 ? newHardware : [createEmptyProduct()];
    softwareProducts.value = newSoftware;
  };

// Expose methods for parent component
// Expose methods/values for parent components
  const getSummary = () => ({
    totalMSRP: totalMSRP.value,
    msrpPercentage: msrpPercentage.value,
  });

  defineExpose({
    getSummary,
  getFormData: () => {
    const requestItems: any[] = [];

    // Helper to push item
    const pushItem = (p: any, isPrimary: 'Y' | 'N', parentId: number | null, isSolution: 'Y' | 'N') => {
      if (!p.itemNumber.value) return;
      requestItems.push({
        requestItemId: p.requestItemId ?? null,
        itemId: p.itemNumber.value,
        dsdQuantity: p.dsdQuantity.value,
        dealerIt: p.dealerITsQuantity.value,
        msrp: p.msrp.value,
        requestedSellingPrice: p.requestSellingPrice.value,
        isPrimaryProposal: isPrimary,
        parentItemId: parentId,
        isSolution,
        displayName:p.displayName
      });
    };

    // Process hardware main units
    hardwareProducts.value.forEach((prod) => {
      const isPrimary = prod.proposalType?.value === 'primary' ? 'Y' : 'N';
      // main hardware product
      pushItem(prod, isPrimary, null, 'N');
      // its accessories
      prod.subProducts.forEach((sub) => pushItem(sub, 'N', Number(prod.itemNumber.value), 'N'));
    });

    // Process software (solutions)
    softwareProducts.value.forEach((prod) => {
      const isPrimary = prod.proposalType?.value === 'primary' ? 'Y' : 'N';
      // main software (solution) product
      pushItem(prod, isPrimary, null, 'Y');
      // its sub-items
      prod.subProducts.forEach((sub) => pushItem(sub, 'N', Number(prod.itemNumber.value), 'Y'));
    });

    return requestItems; // end getFormData
  },
  setFormData
});
</script>

<template>
  <v-container fluid>
    <v-card class="mb-4">
      <v-card-title class="text-h6 bg-grey-lighten-3 d-flex justify-space-between align-center">
        <span>{{ t('page.sales_request_form.worksheets.title') }}</span>
      </v-card-title>
      <v-tabs v-model="currentTab" grow class="mb-0">
        <v-tab value="hardware">{{ t('page.sales_request_form.worksheets.tabs.hardware') }}</v-tab>
        <v-tab value="solutions">{{ t('page.sales_request_form.worksheets.tabs.solutions') }}</v-tab>
        <v-tab value="all">{{ t('page.sales_request_form.worksheets.tabs.all') }}</v-tab>
      </v-tabs>
      <v-divider></v-divider>
      <v-card-text>
        <v-window v-model="currentTab">
          <v-window-item value="all">
            <div class="mb-6">
              <div class="text-h6 mb-3">{{ t('page.sales_request_form.worksheets.total_summary_title') }}</div>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_selling_price') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ totalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ totalMSRP.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_percent_of_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">{{ msrpPercentage.toFixed(2) }}%</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <ProductCategoryCondensedView
              :title="t('page.sales_request_form.worksheets.hardware_title')"
              :products="hardwareProducts"
              productType="hardware"
              :productOptions="hardwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :t="t"
            />
            <ProductCategoryCondensedView
              class="mt-6"
              :title="t('page.sales_request_form.worksheets.solutions_title_condensed')"
              :products="softwareProducts"
              productType="software"
              :productOptions="softwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :t="t"
            />
          </v-window-item>

          <v-window-item value="hardware">
            <div class="mb-6">
              <div class="text-h6 mb-3">{{ t('page.sales_request_form.worksheets.hardware_summary_title') }}</div>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_selling_price') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ hardwareTotalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ hardwareTotalMSRP.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_percent_of_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">{{ hardwareMsrpPercentage.toFixed(2) }}%</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <ProductCategoryExpandedView
              :title="t('page.sales_request_form.worksheets.hardware_title')"
              :products="hardwareProducts"
              productType="hardware"
              :productOptions="hardwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :calculateProductSummaryFunc="calculateProductSummaryForExpandedView"
              :t="t"
            />
          </v-window-item>

          <v-window-item value="solutions">
            <div class="mb-6">
              <div class="text-h6 mb-3">{{ t('page.sales_request_form.worksheets.solutions_summary_title') }}</div>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_selling_price') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ softwareTotalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ softwareTotalMSRP.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_percent_of_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">{{ softwareMsrpPercentage.toFixed(2) }}%</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <ProductCategoryExpandedView
              :title="t('page.sales_request_form.worksheets.solutions_title_expanded')"
              :products="softwareProducts"
              productType="software"
              :productOptions="softwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :calculateProductSummaryFunc="calculateProductSummaryForExpandedView"
              :t="t"
            />
             <div v-if="softwareProducts.length === 0" class="text-center mt-6">
                <v-btn color="primary" @click="addProduct('software')">
                  {{ t('page.sales_request_form.worksheets.buttons.add_main_unit_typed', { unit_type: t('page.sales_request_form.worksheets.solutions_title_expanded') }) }}
                </v-btn>
            </div>
          </v-window-item>
        </v-window>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<style scoped>
  /* ... (styles remain the same) */
.product-container {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 16px;
}

.product-row {
  border-bottom: 1px dashed #e0e0e0;
}

.sub-products {
  padding-left: 16px;
  border-left: 2px solid #2196F3;
}

.sub-product-row {
  margin-top: 8px;
}
</style>
