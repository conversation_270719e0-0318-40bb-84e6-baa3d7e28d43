/**
 * @file Single source of truth for determining which role is allowed
 * to perform specific actions. Each action should have its own method
 * outlined in this file with its corresponding access role assignment.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { Channel_Action, CN_Action, UserRole } from '@/lib/common/types';
import { CanonAuth } from '@/lib/canonAuth';
import { useInternalUserStore } from '@/stores/InternalUserStore';
	const userInternalStore = useInternalUserStore();

/**
 * ----
 * Main
 * ----
*/

// Defines application actions and their allowed roles.
const actions =
{
    // ----------
    // User Login
    // ----------
    [CN_Action.PA_LOGIN] : () =>
    [
        'User.Standard',
        'User.Admin'
    ],

    // ----------
    // Sales Operations
    // ----------
    [Channel_Action.SALES_OPERATIONS] : () =>
    [
        UserRole.SALES_MANAGER,
        UserRole.SALES_REP
    ],

    // ----------
    // Sales Management Approvers
    // ----------
    [Channel_Action.SALES_MANAGER_REGIONAL_LEADERSHIP] : () =>
    [
        UserRole.REGIONAL_LEAD,
        UserRole.SALES_MANAGER,
    ],
    // ----------
    // Sales Management Approvers
    // ----------
    [Channel_Action.SALES_MANAGEMENT] : () =>
    [
        UserRole.REGIONAL_LEAD,
    ],

    // ----------
    // Price Desk Operations
    // ----------
    [Channel_Action.PRICE_DESK_OPERATIONS] : () =>
    [
        UserRole.PRICE_DESK_ANALYST,
    ],

    // ----------
    // Service Desk Operations
    // ----------
    [Channel_Action.SERVICE_DESK_OPERATIONS] : () =>
    [
        UserRole.SERVICE_DESK_ANALYST,
    ],

    // ----------
    // Regional Leadership
    // ----------
    [Channel_Action.REGIONAL_LEADERSHIP] : () =>
    [
        UserRole.REGIONAL_LEAD,

    ],

    // ----------
    // RFP Bid Management
    // ----------
    [Channel_Action.RFP_BID_MANAGEMENT] : () =>
    [
        UserRole.RFP_BID_DESK,

    ],

    // ----------
    // User Role Management
    // ----------
    [Channel_Action.USER_ROLE_MANAGEMENT] : () =>
    [
        UserRole.ADMIN,

    ],

    // -------------------
    // User Profile Update
    // -------------------
    [CN_Action.PA_USER_UPDATE] : () =>
    [
        'User.Standard',
        'User.Admin'
    ],

    // ------------------------------------
    // User Troubleshooting - Non-Sensitive
    // ------------------------------------
    [CN_Action.TROUBLESHOOT] : () =>
    [
        'User.Admin'
    ],

                  // ------------------------------------
    // User with Admin role
    // ------------------------------------
    [CN_Action.ADMIN] : () =>
        [
            'User.Admin'
        ],
};

/**
 * ----
 * Export
 * ----
*/

/**
 * Checks if the currently logged in user has at least one of the required roles
 * defined for the given action.
 *
 * @param {string} [actionId] Unique identifier for each action.
 * 
 * @return {boolean} Value indicating if permission is granted or not.
 */
export default ( actionId : Channel_Action ) : boolean =>
{
    try
    {
        // Get the action's required roles.
        const roles : string[] = actions[ actionId ]();

        if ( roles && roles.length > 0 )
        {
            // Check if any of the required roles exists in user's roles
            return roles.some(role => userInternalStore.roles?.includes(role)) || false;
        }

        // There is no action or the action has no roles defined.
        return false;
        
    }
    catch
    {
        // Invalid action ID was passed.
        return false;
    }
};