<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { debounce } from "lodash";
import { useI18n } from 'vue-i18n';
import { CanonAuth } from "@/lib/canonAuth";
import type { Employee } from '@/lib/common/types';
import { UserRole } from '@/lib/common/types';
import { availableRoles, roleDescriptions } from '@/lib/common/roles';
import { useAppStore } from '@/stores/AppStore';
import { checkUserDetails, adminAddUserUpdateUser, modifyUser, getUserByAccountId, exportUsersAPI } from '@/lib/api';
import { useSnackbarStore } from '@/stores/SnackbarStore';
const appStore = useAppStore();
const { t } = useI18n();
const snackbarStore = useSnackbarStore();

const loadingEmps = ref(false);
const loadingApprovers = ref(false);
const loadingRegionalLeads = ref(false);
const exportLoading = ref(false);
const employees = ref<Employee[]>([]);
const approvers = ref<Employee[]>([]);
const regionalLeads = ref<Employee[]>([]);
const form = ref<{
  employee: Employee | null;
  approver: Employee | null;
  regionalLead: Employee | null;
}>({
  employee: null,
  approver: null,
  regionalLead: null,
});
const dialogVisible = ref(false);
const deviceAgreementId = ref<number | null>(null);
const userName = ref<string>('User');
const currentUser = ref<any>(null);
const selectedRoles = ref<UserRole[]>([]);
const allRoles = availableRoles;

const fetchEmployees = async (searchQuery: string) => {
  if (!searchQuery) {
    form.value.employee = null;
    employees.value = [];
    currentUser.value = null;
    selectedRoles.value = [];
    return;
  }

  if (form.value.employee?.displayName === searchQuery) {
    return;
  }

  try {
    loadingEmps.value = true;
    const response = await CanonAuth.getUsersList(searchQuery);
    employees.value = response.map((user: any) => ({
      id: user.id,
      displayName: user.displayName,
      mail: user.mail,
      mailNickname: user.mailNickname,
      jobTitle: user.jobTitle,
      mobilePhone: user.mobilePhone,
      department: user.department,
      employeeId: user.employeeId,
      office: user.office,
      approverMicrosoftAccountId: user.manager?.id
    }));
  } catch (error) {
    console.error('Error fetching employees:', error);
  } finally {
    loadingEmps.value = false;
  }
};

const fetchApprovers = async (searchQuery: string) => {
  if (!searchQuery) {
    approvers.value = [];
    return;
  }

  if (form.value.approver?.displayName === searchQuery) {
    return;
  }

  try {
    loadingApprovers.value = true;
    const response = await CanonAuth.getUsersList(searchQuery);
    approvers.value = response.map((user: any) => ({
      id: user.id,
      displayName: user.displayName,
      mail: user.mail,
      mailNickname: user.mailNickname,
      jobTitle: user.jobTitle,
      mobilePhone: user.mobilePhone,
      department: user.department,
      employeeId: user.employeeId,
      office: user.office,
      approverMicrosoftAccountId: user.manager?.id
    }));
  } catch (error) {
    console.error('Error fetching approvers:', error);
  } finally {
    loadingApprovers.value = false;
  }
};

const fetchRegionalLeads = async (searchQuery: string) => {
  if (!searchQuery) {
    regionalLeads.value = [];
    return;
  }

  if (form.value.regionalLead?.displayName === searchQuery) {
    return;
  }

  try {
    loadingRegionalLeads.value = true;
    const response = await CanonAuth.getUsersList(searchQuery);
    regionalLeads.value = response.map((user: any) => ({
      id: user.id,
      displayName: user.displayName,
      mail: user.mail,
      mailNickname: user.mailNickname,
      jobTitle: user.jobTitle,
      mobilePhone: user.mobilePhone,
      department: user.department,
      employeeId: user.employeeId,
      office: user.office,
      approverMicrosoftAccountId: user.manager?.id
    }));
  } catch (error) {
    console.error('Error fetching regional leads:', error);
  } finally {
    loadingRegionalLeads.value = false;
  }
};
const fetchUserDetails = async (employee: Employee) => {
  if (!employee) {
    currentUser.value = null;
    selectedRoles.value = [];
    return;
  }
  try {
    appStore.startLoader('checkingUserDetails', t('app.auth.loader.checkingUserDetails'));
    const payload = {
      oid: employee.id,
      name: employee.displayName,
      email: employee.mail || '',
      preferred_username: employee.displayName,
      employeeId: employee.mailNickname,
      approverMicrosoftAccountId: employee.approverMicrosoftAccountId
    };
    try {
      // Try to get the user first
      const userResponse = await getUserByAccountId(employee.id);
      currentUser.value = userResponse.data;
      selectedRoles.value = userResponse.data?.roles || [];

      // Create proper Employee objects for the autocomplete
      if (currentUser.value.regionalLeadAccountId && currentUser.value.regionalLeadName) {
        const regionalLeadEmployee = {
          id: currentUser.value.regionalLeadAccountId,
          displayName: currentUser.value.regionalLeadName,
          mail: '',
          mailNickname: '',
          jobTitle: '',
          mobilePhone: '',
          department: '',
          employeeId: '',
          office: '',
          approverMicrosoftAccountId: ''
        };
        regionalLeads.value = [regionalLeadEmployee];
        form.value.regionalLead = regionalLeadEmployee;
      }

      if (currentUser.value.approverAccountId && currentUser.value.approverName) {
        const approverEmployee = {
          id: currentUser.value.approverAccountId,
          displayName: currentUser.value.approverName,
          mail: '',
          mailNickname: '',
          jobTitle: '',
          mobilePhone: '',
          department: '',
          employeeId: '',
          office: '',
          approverMicrosoftAccountId: ''
        };
        approvers.value = [approverEmployee];
        form.value.approver = approverEmployee;
      }
      
      // Populate approver and regional lead fields if they exist in currentUser
      // await populateApproverAndRegionalLead();
    } catch (err: any) {
      // If not found, add the user
      if (err?.response?.status === 404) {
        try {
          const addResponse = await adminAddUserUpdateUser(payload);
          currentUser.value = addResponse.data;
          selectedRoles.value = addResponse.data?.roles || [];

          // Populate approver and regional lead fields if they exist in currentUser
          await populateApproverAndRegionalLead();
        } catch (addErr) {
          console.error('Error adding new user:', addErr);
          currentUser.value = null;
        }
      } else {
        console.error('Error fetching user details:', err);
        currentUser.value = null;
      }
    } finally {
      appStore.stopLoader('checkingUserDetails');
    }
  } catch (error) {
    console.error('Error fetching user details:', error);
    currentUser.value = null;
  } finally {
    appStore.stopLoader('checkingUserDetails');
  }
};

// Function to populate approver and regional lead fields based on currentUser data
const populateApproverAndRegionalLead = async () => {
  if (!currentUser.value) return;

  // Helper function to map user data
  const mapUserData = (user: any) => ({
    id: user.id,
    displayName: user.displayName,
    mail: user.mail,
    mailNickname: user.mailNickname,
    jobTitle: user.jobTitle,
    mobilePhone: user.mobilePhone,
    department: user.department,
    employeeId: user.employeeId,
    office: user.office,
    approverMicrosoftAccountId: user.manager?.id
  });

  // Populate approver field if approverAccountId exists
  if (currentUser.value.approverAccountId) {
    try {
      // Find employee by account ID for approver
      const approverResponse = await CanonAuth.getUsersList(currentUser.value.approverAccountId);
      if (approverResponse.length > 0) {
        form.value.approver = mapUserData(approverResponse[0]);
      }
    } catch (error) {
      console.error('Error fetching approver details:', error);
    }
  }

  // Populate regional lead field if regionalLeadAccountId exists
  if (currentUser.value.regionalLeadAccountId) {
    try {
      // Find employee by account ID for regional lead
      const regionalLeadResponse = await CanonAuth.getUsersList(currentUser.value.regionalLeadAccountId);
      if (regionalLeadResponse.length > 0) {
        form.value.regionalLead = mapUserData(regionalLeadResponse[0]);
      }
    } catch (error) {
      console.error('Error fetching regional lead details:', error);
    }
  }
};

const saveRoles = async () => {
  if (!currentUser.value) {
    console.error("No user selected to update roles for.");
    return;
  }
  const accountId = currentUser.value.accountId;
  if (!accountId) {
    console.error("No accountId found for current user.");
    return;
  }

  const updatePayload = {
    roles: selectedRoles.value,
    approverAccountId: form.value.approver?.id,
    regionalLeadAccountId: form.value.regionalLead?.id
  };

  try {
    appStore.startLoader('savingRoles', 'Saving roles...');
    await modifyUser(accountId, updatePayload);
    // Show success snackbar
    snackbarStore.show({
      text: 'Roles updated successfully!',
      color: 'success',
      icon: 'check',
      timeout: 2000
    });
  } catch (error) {
    console.error("Failed to save roles:", error);
    snackbarStore.show({
      text: 'Failed to save roles.',
      color: 'error',
      icon: 'alert',
      timeout: 2000
    });
  } finally {
    appStore.stopLoader('savingRoles');
  }
};

const exportUsers = async () => {
  try {
    exportLoading.value = true;

const response =await exportUsersAPI()


    // Get the blob from response
const blob = new Blob([response.data], {
  type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
});
const url = URL.createObjectURL(blob);
const link = document.createElement('a');

let filename = 'users_export.xlsx';
const contentDisposition = response.headers['content-disposition'];
if (contentDisposition) {
  const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
  if (filenameMatch) {
    filename = filenameMatch[1];
  }
}

link.href = url;
link.download = filename;
document.body.appendChild(link);
link.click();
document.body.removeChild(link);
URL.revokeObjectURL(url);
    snackbarStore.show({
      text: 'Users exported successfully!',
      color: 'success',
      icon: 'download',
      timeout: 2000
    });

  } catch (error) {
    console.error('Error exporting users:', error);
    snackbarStore.show({
      text: 'Failed to export users.',
      color: 'error',
      icon: 'alert',
      timeout: 2000
    });
  } finally {
    exportLoading.value = false;
  }
};

const debouncedFetchEmployees = debounce(fetchEmployees, 400);
const debouncedFetchApprovers = debounce(fetchApprovers, 400);
const debouncedFetchRegionalLeads = debounce(fetchRegionalLeads, 400);

const employeeDetails = computed(() => {
  const employee = form.value.employee;
  if (!employee) {
    return {};
  }
  return {
    "Name": employee.displayName,
    "Department": employee.department,
    "ID": employee.mailNickname,
    "E-mail": employee.mail,
  };
});
// Load data
onMounted(() => {
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="10" md="8">
        <v-card class="elevation-2">
          <v-card-title class="text-h5 py-4 bg-grey-lighten-4 d-flex align-center justify-space-between">
            <span>User Access Management</span>
            <v-btn
              color="primary"
              variant="outlined"
              prepend-icon="mdi-download"
              :loading="exportLoading"
              @click="exportUsers"
              size="small"
            >
              Export Users
            </v-btn>
          </v-card-title>
          <v-card-text class="pa-5">
            <v-autocomplete
              v-model="form.employee"
              :label="t('channelS.form.employeeNameOrId')"
              :items="employees"
              item-title="displayName"
              item-value="id"
              return-object
              no-filter
              :filterable="false"
              @update:search="debouncedFetchEmployees"
              @update:modelValue="form.employee && fetchUserDetails(form.employee)"
              :loading="loadingEmps"
              clearable
              variant="outlined"
              density="compact"
            >
              <template v-slot:append-inner>
                <v-progress-circular v-if="loadingEmps" indeterminate size="24"></v-progress-circular>
              </template>
              <template v-slot:item="{ props, item }">
                <v-list-item v-bind="props">
                  <v-list-item-title>
                    <span class="font-weight-bold">{{ item?.raw?.displayName }}</span>
                    <span v-if="item?.raw?.mailNickname"> ({{ item?.raw?.mailNickname }})</span>
                    <span v-if="item?.raw?.mail"> | {{ item?.raw?.mail }}</span>
                  </v-list-item-title>
                  <v-list-item-subtitle v-if="item?.raw?.department || item?.raw?.jobTitle">
                    <span v-if="item?.raw?.department">{{ item?.raw?.department }}</span>
                    <span v-if="item?.raw?.department && item?.raw?.jobTitle">, </span>
                    <span v-if="item?.raw?.jobTitle">{{ item?.raw?.jobTitle }}</span>
                  </v-list-item-subtitle>
                </v-list-item>
              </template>
              <template v-slot:no-data>
                <div class="text-center pa-4 grey--text">No users found.</div>
              </template>
            </v-autocomplete>

            <template v-if="currentUser && form.employee">
              <v-container class="pa-0 mt-5">
                <v-row v-for="(value, label) in employeeDetails" :key="label" dense>
                  <v-col cols="4" class="font-weight-bold">{{ t(`channelS.form.employeeDetails.${label.toLowerCase()}`) }}</v-col>
                  <v-col cols="8">{{ value }}</v-col>
                </v-row>
              </v-container>

              <v-divider class="my-6"></v-divider>

           
              <div >
                <h3 class="text-h6">Manage Roles</h3>
                <p class="text-body-2 mb-4">Select roles to assign to the user.</p>
                <v-row>
                  <v-col v-for="role in allRoles" :key="role.value" cols="12" sm="6" md="4">
                    <v-tooltip location="top" :text="role.description">
                      <template #activator="{ props }">
                        <v-checkbox
                          v-model="selectedRoles"
                          v-bind="props"
                          :label="role.label"
                          :value="role.value"
                          :disabled="role.value === 'STANDARD'"
                          hide-details
                          density="compact"
                        ></v-checkbox>
                      </template>
                    </v-tooltip>
                  </v-col>
                </v-row>
              </div>
                 <!-- Approver and Regional Lead Selection -->
              <v-row class="mb-4" v-if="selectedRoles.includes(UserRole.SALES_REP) || selectedRoles.includes(UserRole.SALES_MANAGER)">
                <v-col cols="12" md="6" v-if="selectedRoles.includes(UserRole.SALES_REP)">
                  <v-autocomplete
                    v-model="form.approver"
                    label="Approver"
                    :items="approvers"
                    item-title="displayName"
                    item-value="id"
                    return-object
                    no-filter
                    :filterable="false"
                    @update:search="debouncedFetchApprovers"
                    :loading="loadingApprovers"
                    clearable
                    variant="outlined"
                    density="compact"
                  >
                    <template v-slot:append-inner>
                      <v-progress-circular v-if="loadingApprovers" indeterminate size="24"></v-progress-circular>
                    </template>
                    <template v-slot:item="{ props, item }">
                      <v-list-item v-bind="props">
                        <v-list-item-title>
                          <span class="font-weight-bold">{{ item?.raw?.displayName }}</span>
                          <span v-if="item?.raw?.mailNickname"> ({{ item?.raw?.mailNickname }})</span>
                          <span v-if="item?.raw?.mail"> | {{ item?.raw?.mail }}</span>
                        </v-list-item-title>
                        <v-list-item-subtitle v-if="item?.raw?.department || item?.raw?.jobTitle">
                          <span v-if="item?.raw?.department">{{ item?.raw?.department }}</span>
                          <span v-if="item?.raw?.department && item?.raw?.jobTitle">, </span>
                          <span v-if="item?.raw?.jobTitle">{{ item?.raw?.jobTitle }}</span>
                        </v-list-item-subtitle>
                      </v-list-item>
                    </template>
                    <template v-slot:no-data>
                      <div class="text-center pa-4 grey--text">No users found.</div>
                    </template>
                  </v-autocomplete>
                </v-col>

                <v-col cols="12" md="6" v-if="selectedRoles.includes(UserRole.SALES_MANAGER) || selectedRoles.includes(UserRole.SALES_REP)">
                  <v-autocomplete
                    v-model="form.regionalLead"
                    label="Regional Lead"
                    :items="regionalLeads"
                    item-title="displayName"
                    item-value="id"
                    return-object
                    no-filter
                    :filterable="false"
                    @update:search="debouncedFetchRegionalLeads"
                    :loading="loadingRegionalLeads"
                    clearable
                    variant="outlined"
                    density="compact"
                  >
                    <template v-slot:append-inner>
                      <v-progress-circular v-if="loadingRegionalLeads" indeterminate size="24"></v-progress-circular>
                    </template>
                    <template v-slot:item="{ props, item }">
                      <v-list-item v-bind="props">
                        <v-list-item-title>
                          <span class="font-weight-bold">{{ item?.raw?.displayName }}</span>
                          <span v-if="item?.raw?.mailNickname"> ({{ item?.raw?.mailNickname }})</span>
                          <span v-if="item?.raw?.mail"> | {{ item?.raw?.mail }}</span>
                        </v-list-item-title>
                        <v-list-item-subtitle v-if="item?.raw?.department || item?.raw?.jobTitle">
                          <span v-if="item?.raw?.department">{{ item?.raw?.department }}</span>
                          <span v-if="item?.raw?.department && item?.raw?.jobTitle">, </span>
                          <span v-if="item?.raw?.jobTitle">{{ item?.raw?.jobTitle }}</span>
                        </v-list-item-subtitle>
                      </v-list-item>
                    </template>
                    <template v-slot:no-data>
                      <div class="text-center pa-4 grey--text">No users found.</div>
                    </template>
                  </v-autocomplete>
                </v-col>
              </v-row>

            </template>
          </v-card-text>

          <v-card-actions class="pa-4 bg-grey-lighten-4">
            <v-spacer></v-spacer>
            <v-btn 
              color="primary" 
              variant="elevated" 
              @click="saveRoles" 
              :disabled="!currentUser"
              size="large"
            >
              Save
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
/* Add any necessary styles here */
</style>
      
