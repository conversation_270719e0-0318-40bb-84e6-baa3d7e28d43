<script setup lang="ts">
import type { WorkSheetProduct } from '@/lib/common/types';
import { ref, onMounted, defineExpose, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { getServiceForm } from '@/services/salesRequestService';
import ServiceRequestPreview from './ServiceRequestPreview.vue';

const { t } = useI18n();



// Define props to receive data from parent component
const props = defineProps({
  customerDetails: {
    type: Object,
    default: () => ({})
  },
  paymentDetails: {
    type: Object,
    default: () => ({})
  },
  equipmentDetails: {
    type: Object,
    default: () => ({})
  },
  worksheetDetails: {
    type: Array as () => any[],
    default: () => []
  },
  serviceRequestData: {
    type: Object,
    default: () => ({
      service_request_form: {},
      toner_and_service_value_pack: {},
      approvals_details: {},
      product_service_details: { models_overview: [], accessories_included: [] },
      competitive_current_usage_info: {},
      current_equipment_details: {},
      service_business_case: {}
    })
  },
  requestId: {
    type: [Number, String],
    required: false
  },
  serviceFormId: {
    type: [Number, String],
    required: false
  }
});

// Initialize expanded state for worksheet products
watch(() => props.worksheetDetails, (newWorksheetDetails) => {
  if (newWorksheetDetails && Array.isArray(newWorksheetDetails)) {
    newWorksheetDetails.forEach(product => {
      product._expanded = false;
    });
  }
}, { immediate: true });

// Accordion expansion state
const customerPanel = ref(true);
const paymentPanel = ref(false);
const equipmentPanel = ref(false);
const worksheetPanel = ref(false);
const serviceRequestPanel = ref<number | null>(null); // null = collapsed, 0 = expanded

// Service request data state
const serviceRequestData = ref({
  serviceFormId: null,
  requestId: null,
  servicePackId: null,
  tonerType: null,
  tonerTypeValue: null,
  servicePackDescription: null,
  region: null,
  territory: null,
  coterm: null,
  isDealerAcceptedSr: null,
  paymentMode: null,
  paymentModeValue: null,
  leaseTermInMonth: null,
  msrp: null,
  msrpPercent: null,
  currentUsageInformation: null,
  currentEquipmentInfo: null,
  serviceBusinessCase: null,
  serviceApprovals: [],
  dsdRequestMfpPricing: []
});

const isServiceRequestLoading = ref(false);
const serviceRequestLoadError = ref('');
const hasServiceRequestLoaded = ref(false);

// Function to fetch service form data
const fetchServiceFormData = async () => {
  if (!props.serviceFormId || hasServiceRequestLoaded.value || isServiceRequestLoading.value) {
    return;
  }

  isServiceRequestLoading.value = true;
  serviceRequestLoadError.value = '';

  try {
    const response = await getServiceForm(Number(props.serviceFormId));
    serviceRequestData.value = response.data || serviceRequestData.value;
    hasServiceRequestLoaded.value = true;
  } catch (error) {
    console.error('Failed to fetch service form data:', error);
    serviceRequestLoadError.value = 'Failed to load service request data';
  } finally {
    isServiceRequestLoading.value = false;
  }
};

// Watch for service request panel expansion to trigger data loading
watch(() => serviceRequestPanel.value, (newValue) => {
  // newValue is 0 when expanded, null/undefined when collapsed
  if (newValue === 0 && props.serviceFormId) {
    fetchServiceFormData();
  }
});

// Also watch for serviceFormId changes
watch(() => props.serviceFormId, (newValue) => {
  if (newValue && serviceRequestPanel.value === 0) {
    hasServiceRequestLoaded.value = false; // Reset to allow refetch
    fetchServiceFormData();
  }
});

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Transform flat worksheet data into hierarchical structure
const hierarchicalWorksheetData = computed(() => {
  if (!props.worksheetDetails || props.worksheetDetails.length === 0) {
    return [];
  }

  // Group items by parentItemId
  const mainUnits = props.worksheetDetails.filter(item => item.parentItemId === null);
  const subUnits = props.worksheetDetails.filter(item => item.parentItemId !== null);

  // Create hierarchical structure
  return mainUnits.map(mainUnit => {
    const children = subUnits.filter(subUnit => subUnit.parentItemId === mainUnit.itemId);
    return {
      ...mainUnit,
      subProducts: children,
      _expanded: true // Initially expanded as per requirement
    };
  });
});

// Format percentage values
const formatPercentage = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return `${value}%`;
};

// Format array values for display
const formatArray = (arr: any[] | null | undefined) => {
  if (!arr || !Array.isArray(arr) || arr.length === 0) return 'None';
  return arr.join(', ');
};

// Format object values for display
const formatValue = (value: any) => {
  if (value === null || value === undefined) return 'N/A';
  if (Array.isArray(value)) return formatArray(value);
  if (typeof value === 'object' && value.value !== undefined) return value.value || 'N/A';
  return value || 'N/A';
};

// Define arrays of keys to show for each accordion
const customerKeysToShow = [
  'businessId',
  'legalName',
  'salesforceOppId',
  'customerRelationship',
  'rfp',
  'rfpDueDate',
  'locationId',
  'customerWebsite',
  'globalAgreement',
  'branch',
  'printAssessment',
  'coreItsNetworkScan',
  'msa',
    'salesRepOid',
  'salesManagerOid',
  'regionalLeaderOid',
  'salesChannel',
  'portfolioId'
];

const paymentKeysToShow = [
  'pricingCategoryCode',
  'minimumCommitment',
  'paymentMode',
  'paymentJustification',
  'leaseTermInMonth',
  'typeOfContract',
  'billingPeriod',
  'supplierType',
  'implementation',
  'foreignBuyoutTradeUp',
  'proposalDate'
];

const strategyKeysToShow = [
  'potentialUnitCount',
  'installationDate',
  'specialConsideration',
  'hardwarePricingStrategy',
  'softwareSalesStrategy',
  'currentIncumbent'
];

// Helper function to check if a key should be displayed
const shouldShowKey = (key: string, allowedKeys: string[]): boolean => {
  return allowedKeys.includes(key);
};

// Lookup maps for ID-to-name conversions
const businessLookup = ref<Record<string, string>>({});
const locationLookup = ref<Record<string, string>>({});
const portfolioLookup = ref<Record<string, string>>({});
const salesRepLookup = ref<Record<string, string>>({});
const salesManagerLookup = ref<Record<string, string>>({});
const regionalLeaderLookup = ref<Record<string, string>>({});

// Keys that need lookup conversion
const keysNeedingLookup: Record<string, any> = {
  'businessId': businessLookup,
  'locationId': locationLookup,
  'portfolioId': portfolioLookup,
  'salesRepOid': salesRepLookup,
  'salesManagerOid': salesManagerLookup,
  'regionalLeaderOid': regionalLeaderLookup
};

// Enhanced format function that handles lookups
const formatValueWithLookup = (value: any, key: string): string => {
  // First apply the existing formatValue logic
  let formattedValue = formatValue(value);

  // If this key needs lookup and we have a lookup map for it
  if (keysNeedingLookup[key] && keysNeedingLookup[key].value[value]) {
    return keysNeedingLookup[key].value[value];
  }

  // For boolean values, convert to Yes/No
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  return formattedValue;
};

// Expose methods for parent component
defineExpose({
  refreshData: () => {
    // This method can be called by parent to refresh data if needed
    console.log('Refreshing preview data');
  }
});
</script>

<template>
  <v-container fluid>
    <!-- Customer Details Section -->
    <v-expansion-panels v-model="customerPanel">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">person</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.customer_details') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-table density="compact">
            <tbody>
              <tr v-for="(value, key) in customerDetails" :key="key" v-show="shouldShowKey(key, customerKeysToShow)">
                <th class="text-left">{{ t(`page.sales_request_form.customer_details.${key}`) || key }}</th>
                <td class="text-left">{{ formatValueWithLookup(value, key) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Payment Details Section -->
    <v-expansion-panels v-model="paymentPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">payments</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.payment_details') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-table density="compact">
            <tbody>
              <tr v-for="(value, key) in paymentDetails" :key="key" v-show="shouldShowKey(key, paymentKeysToShow)">
                <th class="text-left">{{ t(`page.sales_request_form.payment_details.${key}`) || key }}</th>
                <td class="text-left">{{ formatValueWithLookup(value, key) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Equipment Details Section -->
    <v-expansion-panels v-model="equipmentPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">devices</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.equipments') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <!-- Equipment Basic Info -->
          <h3 class="text-subtitle-1 mb-2">Equipment Information</h3>
          <v-table density="compact" class="mb-4">
            <tbody>
              <tr v-if="equipmentDetails?.equipment">
                <th class="text-left">Selling Price</th>
                <td class="text-left">{{ formatCurrency(equipmentDetails.equipment.sellingPrice) }}</td>
              </tr>
              <tr v-if="equipmentDetails?.equipment">
                <th class="text-left">MSRP</th>
                <td class="text-left">{{ formatCurrency(equipmentDetails.equipment.msrp) }}</td>
              </tr>
              <tr v-if="equipmentDetails?.equipment">
                <th class="text-left">% of MSRP</th>
                <td class="text-left">{{ equipmentDetails.equipment.percentOfMsrp }}</td>
              </tr>
              <tr>
                <th class="text-left">Potential Competition</th>
                <td class="text-left">{{ formatArray(equipmentDetails?.competition) }}</td>
              </tr>
              <tr>
                <th class="text-left">MFP Incumbent</th>
                <td class="text-left">{{ equipmentDetails?.mfpIncumbent || 'N/A' }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- Installation Details -->
          <h3 class="text-subtitle-1 mb-2">Installation Details</h3>
          <v-table density="compact" class="mb-4">
            <tbody>
              <tr>
                <th class="text-left">Potential Units</th>
                <td class="text-left">{{ equipmentDetails?.potentialUnits || 'N/A' }}</td>
              </tr>
              <tr>
                <th class="text-left">Installation Date</th>
                <td class="text-left">{{ equipmentDetails?.installationDate || 'N/A' }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- Strategy Information -->
          <h3 class="text-subtitle-1 mb-2">Strategy Information</h3>
          <v-table density="compact" class="mb-4">
            <tbody>
              <tr v-for="(value, key) in equipmentDetails" :key="key" v-show="shouldShowKey(key, strategyKeysToShow)">
                <th class="text-left">{{ t(`page.sales_request_form.equipment_details.${key}`) || key }}</th>
                <td class="text-left">{{ formatValueWithLookup(value, key) }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- Equipment Distribution Table -->
          <h3 class="text-subtitle-1 mb-2">Equipment Distribution</h3>
          <v-table density="compact" v-if="equipmentDetails?.tableData">
            <thead>
              <tr>
                <th class="text-left">Category</th>
                <th class="text-left">Units</th>
                <th v-for="(vendor, index) in ['Canon BSS', 'Canon Dealer', 'Xerox', 'Ricoh', 'Sharp', 'Konica M.']" :key="index" class="text-left">
                  {{ vendor }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in equipmentDetails.tableData" :key="index">
                <td>{{ row.category }}</td>
                <td>{{ row.units }}</td>
                <td v-for="(value, vIndex) in row.values" :key="vIndex">
                  {{ formatPercentage(value) }}
                </td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Worksheet Details Section -->
    <v-expansion-panels v-model="worksheetPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">description</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.worksheets') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div v-if="hierarchicalWorksheetData?.length > 0">
            <!-- Main products table with expandable rows -->
            <v-table density="compact" class="mb-4">
              <thead>
                <tr>
                  <th width="40" class="text-center"></th>
                  <th class="text-left">Item ID</th>
                  <th class="text-left">Display Name</th>
                  <th class="text-left">DSD Quantity</th>
                  <th class="text-left">Dealer Quantity</th>
                  <th class="text-left">MSRP</th>
                  <th class="text-left">Requested Price</th>
                  <th class="text-left">Type</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="(mainUnit, mainIndex) in hierarchicalWorksheetData" :key="mainIndex">
                  <!-- Main Unit Row -->
                  <tr class="main-unit-row">
                    <td class="text-center">
                      <v-icon
                        v-if="mainUnit.subProducts && mainUnit.subProducts.length > 0"
                        size="small"
                        :color="mainUnit.isSolution === 'Y' ? 'blue' : 'green'"
                      >
                        expand_less
                      </v-icon>
                    </td>
                    <td class="font-weight-bold">{{ mainUnit.itemId }}</td>
                    <td class="font-weight-bold" :class="{ 'text-blue': mainUnit.isSolution === 'Y', 'text-green': mainUnit.isSolution === 'N' }">
                      {{ mainUnit.displayName }}
                    </td>
                    <td class="font-weight-bold">{{ mainUnit.dsdQuantity || 0 }}</td>
                    <td class="font-weight-bold">{{ mainUnit.dealerIt || 0 }}</td>
                    <td class="font-weight-bold">{{ formatCurrency(mainUnit.msrp) }}</td>
                    <td class="font-weight-bold">{{ formatCurrency(mainUnit.requestedSellingPrice) }}</td>
                    <td>
                      <v-chip
                        size="small"
                        :color="mainUnit.isSolution === 'Y' ? 'blue' : 'green'"
                        variant="tonal"
                      >
                        {{ mainUnit.isSolution === 'Y' ? 'Solution' : 'Hardware' }}
                      </v-chip>
                    </td>
                  </tr>

                  <!-- Sub Units Rows (Always shown since _expanded is true) -->
                  <template v-if="mainUnit.subProducts && mainUnit.subProducts.length > 0">
                    <tr
                      v-for="subUnit in mainUnit.subProducts"
                      :key="subUnit.requestItemId"
                      class="sub-unit-row"
                    >
                      <td class="text-center">
                        <v-icon size="small" class="text-grey">subdirectory_arrow_right</v-icon>
                      </td>
                      <td class="pl-4 text-grey-darken-1">{{ subUnit.itemId }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.displayName }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.dsdQuantity || 0 }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.dealerIt || 0 }}</td>
                      <td class="text-grey-darken-1">{{ formatCurrency(subUnit.msrp) }}</td>
                      <td class="text-grey-darken-1">{{ formatCurrency(subUnit.requestedSellingPrice) }}</td>
                      <td>
                        <v-chip
                          size="small"
                          :color="subUnit.isSolution === 'Y' ? 'blue' : 'green'"
                          variant="outlined"
                        >
                          {{ subUnit.isSolution === 'Y' ? 'Solution' : 'Hardware' }}
                        </v-chip>
                      </td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </v-table>
          </div>
          <v-alert v-else type="info" class="mt-2">
            No worksheet data available
          </v-alert>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Service Request Details Section -->
    <v-expansion-panels v-model="serviceRequestPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">assignment</v-icon>
            <span class="text-h6">Service Request Details</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div v-if="isServiceRequestLoading" class="text-center pa-4">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <div class="mt-2">Loading service request data...</div>
          </div>
          <v-alert v-else-if="serviceRequestLoadError" type="error" class="ma-4">
            {{ serviceRequestLoadError }}
          </v-alert>
          <ServiceRequestPreview
            v-else
            :service-request-data="serviceRequestData"
          />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-container>
</template>

<style scoped>
.v-expansion-panel-title {
  background-color: #f5f5f5;
}

.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.main-unit-row {
  background-color: #f5f5f5;
  border-left: 4px solid #4caf50;
}

.main-unit-row.solution {
  border-left-color: #2196f3;
}

.sub-unit-row {
  background-color: #fafafa;
  border-left: 2px solid #e0e0e0;
}

.sub-unit-row td {
  font-size: 0.875rem;
  color: #6c757d;
}

.text-blue {
  color: #2196f3 !important;
}

.text-green {
  color: #4caf50 !important;
}

.rotate-45 {
  transform: rotate(45deg);
  transition: transform 0.2s;
}
</style>









