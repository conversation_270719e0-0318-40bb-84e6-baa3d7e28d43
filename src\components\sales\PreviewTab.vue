<script setup lang="ts">
import type { WorkSheetProduct } from '@/lib/common/types';
import { ref, onMounted, defineExpose, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { getServiceForm } from '@/services/salesRequestService';
import ServiceRequestPreview from './ServiceRequestPreview.vue';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';

const { t } = useI18n();


// Dropdown option refs for LOV-driven fields
const channelOptions = ref<{ title: string; value: string }[]>([]);
const portfolioOptions = ref<{ title: string; value: string }[]>([]);
const branchOptions = ref<{ title: string; value: string }[]>([]);
onMounted(async () => {
    const lovPromises = [
        getLov(LovCategories.SALES_CHANNEL),
        getLov(LovCategories.PORTFOLIO),
        getLov(LovCategories.BRANCH)
    ];
    try{
        const [channelLovs, portfolioLovs, branchLovs] = await Promise.all(lovPromises);
       channelOptions.value = channelLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        portfolioOptions.value = portfolioLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        branchOptions.value = branchLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
    
    }
    catch {
      
    }
})
// Define props to receive data from parent component
const props = defineProps({
  customerDetails: {
    type: Object,
    default: () => ({})
  },
  paymentDetails: {
    type: Object,
    default: () => ({})
  },
  equipmentDetails: {
    type: Object,
    default: () => ({})
  },
  worksheetDetails: {
    type: Array as () => any[],
    default: () => []
  },
  serviceRequestData: {
    type: Object,
    default: () => ({
      service_request_form: {},
      toner_and_service_value_pack: {},
      approvals_details: {},
      product_service_details: { models_overview: [], accessories_included: [] },
      competitive_current_usage_info: {},
      current_equipment_details: {},
      service_business_case: {}
    })
  },
  requestId: {
    type: [Number, String],
    required: false
  },
  serviceFormId: {
    type: [Number, String],
    required: false
  }
});

// Initialize expanded state for worksheet products
watch(() => props.worksheetDetails, (newWorksheetDetails) => {
  if (newWorksheetDetails && Array.isArray(newWorksheetDetails)) {
    newWorksheetDetails.forEach(product => {
      product._expanded = false;
    });
  }
}, { immediate: true });

// Accordion expansion state
const customerPanel = ref(true);
const paymentPanel = ref(false);
const equipmentPanel = ref(false);
const worksheetPanel = ref(false);
const serviceRequestPanel = ref<number | null>(null); // null = collapsed, 0 = expanded

// Service request data state
const serviceRequestData = ref({
  serviceFormId: null,
  requestId: null,
  servicePackId: null,
  tonerType: null,
  tonerTypeValue: null,
  servicePackDescription: null,
  region: null,
  territory: null,
  coterm: null,
  isDealerAcceptedSr: null,
  paymentMode: null,
  paymentModeValue: null,
  leaseTermInMonth: null,
  msrp: null,
  msrpPercent: null,
  currentUsageInformation: null,
  currentEquipmentInfo: null,
  serviceBusinessCase: null,
  serviceApprovals: [],
  dsdRequestMfpPricing: []
});

const isServiceRequestLoading = ref(false);
const serviceRequestLoadError = ref('');
const hasServiceRequestLoaded = ref(false);

// Function to fetch service form data
const fetchServiceFormData = async () => {
  if (!props.serviceFormId || hasServiceRequestLoaded.value || isServiceRequestLoading.value) {
    return;
  }

  isServiceRequestLoading.value = true;
  serviceRequestLoadError.value = '';

  try {
    const response = await getServiceForm(Number(props.serviceFormId));
    serviceRequestData.value = response.data || serviceRequestData.value;
    hasServiceRequestLoaded.value = true;
  } catch (error) {
    console.error('Failed to fetch service form data:', error);
    serviceRequestLoadError.value = 'Failed to load service request data';
  } finally {
    isServiceRequestLoading.value = false;
  }
};

// Watch for service request panel expansion to trigger data loading
watch(() => serviceRequestPanel.value, (newValue) => {
  // newValue is 0 when expanded, null/undefined when collapsed
  if (newValue === 0 && props.serviceFormId) {
    fetchServiceFormData();
  }
});

// Also watch for serviceFormId changes
watch(() => props.serviceFormId, (newValue) => {
  if (newValue && serviceRequestPanel.value === 0) {
    hasServiceRequestLoaded.value = false; // Reset to allow refetch
    fetchServiceFormData();
  }
});

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Transform flat worksheet data into hierarchical structure
const hierarchicalWorksheetData = computed(() => {
  if (!props.worksheetDetails || props.worksheetDetails.length === 0) {
    return [];
  }

  // Group items by parentItemId
  const mainUnits = props.worksheetDetails.filter(item => item.parentItemId === null);
  const subUnits = props.worksheetDetails.filter(item => item.parentItemId !== null);

  // Create hierarchical structure
  return mainUnits.map(mainUnit => {
    const children = subUnits.filter(subUnit => subUnit.parentItemId === mainUnit.itemId);
    return {
      ...mainUnit,
      subProducts: children,
      _expanded: true // Initially expanded as per requirement
    };
  });
});

// Format percentage values
const formatPercentage = (value: number | null | undefined) => {
  if (!value && value !== 0) return 'N/A';
  return `${value}%`;
};

// Format array values for display
const formatArray = (arr: any[] | null | undefined) => {
  if (!arr || !Array.isArray(arr) || arr.length === 0) return 'None';
  return arr.join(', ');
};

// Format object values for display
const formatValue = (value: any) => {
  if (value === null || value === undefined) return 'N/A';
  if (Array.isArray(value)) return formatArray(value);
  if (typeof value === 'object' && value.value !== undefined) return value.value || 'N/A';
  return value || 'N/A';
};

const fomAddress =(location:any)=>{
  if (!location) return 'N/A';
  return location.addressLine1 + ', ' + location.city + ', '+ location.country + ', ' + location.state + ', ' + location.postalCode;
}
// Helper function to format boolean values
const formatBooleanValue = (value: any): string => {
  if (typeof value === 'string') {
    return value=='Y' ? 'Yes' : 'No';
  }
  return formatValue(value);
};

// Helper function to format date values
const formatDate = (dateValue: any): string => {
  if (!dateValue || dateValue === null || dateValue === undefined) {
    return 'N/A';
  }

  try {
    // Handle different date formats
    let date: Date;

    if (dateValue instanceof Date) {
      date = dateValue;
    } else if (typeof dateValue === 'string') {
      // Handle ISO string or other string formats
      date = new Date(dateValue);
    } else if (typeof dateValue === 'object' && dateValue.value) {
      // Handle object with value property
      date = new Date(dateValue.value);
    } else {
      // Try to parse as date
      date = new Date(dateValue);
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }

    // Format date as MM/DD/YYYY
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });

  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

// Computed properties for worksheet totals
const solutionTotals = computed(() => {
  if (!hierarchicalWorksheetData.value || hierarchicalWorksheetData.value.length === 0) {
    return { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };
  }

  let totals = { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };

  hierarchicalWorksheetData.value.forEach(mainUnit => {
    // Include main unit if it's a solution
    if (mainUnit.isSolution === 'Y') {
      totals.dsdQuantity += Number(mainUnit.dsdQuantity) || 0;
      totals.dealerQuantity += Number(mainUnit.dealerIt) || 0;
      totals.msrp += Number(mainUnit.msrp) || 0;
      totals.requestedPrice += Number(mainUnit.requestedSellingPrice) || 0;
    }

    // Include sub-products if they are solutions
    if (mainUnit.subProducts && mainUnit.subProducts.length > 0) {
      mainUnit.subProducts.forEach(subUnit => {
        if (subUnit.isSolution === 'Y') {
          totals.dsdQuantity += Number(subUnit.dsdQuantity) || 0;
          totals.dealerQuantity += Number(subUnit.dealerIt) || 0;
          totals.msrp += Number(subUnit.msrp) || 0;
          totals.requestedPrice += Number(subUnit.requestedSellingPrice) || 0;
        }
      });
    }
  });

  return totals;
});

const hardwareTotals = computed(() => {
  if (!hierarchicalWorksheetData.value || hierarchicalWorksheetData.value.length === 0) {
    return { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };
  }

  let totals = { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };

  hierarchicalWorksheetData.value.forEach(mainUnit => {
    // Include main unit if it's hardware
    if (mainUnit.isSolution === 'N') {
      totals.dsdQuantity += Number(mainUnit.dsdQuantity) || 0;
      totals.dealerQuantity += Number(mainUnit.dealerIt) || 0;
      totals.msrp += Number(mainUnit.msrp) || 0;
      totals.requestedPrice += Number(mainUnit.requestedSellingPrice) || 0;
    }

    // Include sub-products if they are hardware
    if (mainUnit.subProducts && mainUnit.subProducts.length > 0) {
      mainUnit.subProducts.forEach(subUnit => {
        if (subUnit.isSolution === 'N') {
          totals.dsdQuantity += Number(subUnit.dsdQuantity) || 0;
          totals.dealerQuantity += Number(subUnit.dealerIt) || 0;
          totals.msrp += Number(subUnit.msrp) || 0;
          totals.requestedPrice += Number(subUnit.requestedSellingPrice) || 0;
        }
      });
    }
  });

  return totals;
});

const grandTotals = computed(() => {
  return {
    dsdQuantity: solutionTotals.value.dsdQuantity + hardwareTotals.value.dsdQuantity,
    dealerQuantity: solutionTotals.value.dealerQuantity + hardwareTotals.value.dealerQuantity,
    msrp: solutionTotals.value.msrp + hardwareTotals.value.msrp,
    requestedPrice: solutionTotals.value.requestedPrice + hardwareTotals.value.requestedPrice
  };
});

// Expose methods for parent component
defineExpose({
  refreshData: () => {
    // This method can be called by parent to refresh data if needed
    console.log('Refreshing preview data');
  }
});
</script>

<template>
  <v-container fluid>
    <!-- Customer Details Section -->
    <v-expansion-panels v-model="customerPanel">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">person</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.customer_details') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-table density="compact">
            <tbody>
              <tr>
                <th class="text-left">Business Name</th>
                <td class="text-left">{{ formatValue(customerDetails.legalName) }}</td>
              </tr>
              <tr>
                <th class="text-left">Legal Name</th>
                <td class="text-left">{{ formatValue(customerDetails.legalName) }}</td>
              </tr>
              <tr>
                <th class="text-left">Salesforce Opportunity ID</th>
                <td class="text-left">{{ formatValue(customerDetails.salesforceOppId) }}</td>
              </tr>
              <tr>
                <th class="text-left">Customer Relationship</th>
                <td class="text-left">{{ formatValue(customerDetails.customer.relationshipStatus) }}</td>
              </tr>
              <tr>
                <th class="text-left">RFP</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails.isRfp) }}</td>
              </tr>
              <tr>
                <th class="text-left">RFP Due Date</th>
                <td class="text-left">{{ formatDate(customerDetails.rfpDueDate) }}</td>
              </tr>
              <tr>
                <th class="text-left">Location</th>
                <td class="text-left">{{ fomAddress(customerDetails.location) }}</td>
              </tr>
              <tr>
                <th class="text-left">Customer Website</th>
                <td class="text-left">{{ formatValue(customerDetails.customer.website) }}</td>
              </tr>
              <tr>
                <th class="text-left">Global Agreement</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails.customer.isGlobalAgreement) }}</td>
              </tr>
              <tr>
                <th class="text-left">Branch</th>
                <td class="text-left">{{ formatValue(customerDetails.salesBranch) }}</td>
              </tr>
              <tr>
                <th class="text-left">Sales Representative</th>
                <td class="text-left">{{ formatValue(customerDetails.salesRepOid) }}</td>
              </tr>
              <tr>
                <th class="text-left">Sales Manager</th>
                <td class="text-left">{{ formatValue(customerDetails.salesManagerOid) }}</td>
              </tr>
              <tr>
                <th class="text-left">Regional Leader</th>
                <td class="text-left">{{ formatValue(customerDetails.regionalLeaderOid) }}</td>
              </tr>
              <tr>
                <th class="text-left">Sales Channel</th>
                <td class="text-left">{{ formatValue(customerDetails.salesChannelValue) }}</td>
              </tr>
              <tr>
                <th class="text-left">Portfolio</th>
                <td class="text-left">{{ formatValue(customerDetails.portfolio.name) }}</td>
              </tr>
              <tr>
                <th class="text-left">Print Assessment</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails.printAssessment) }}</td>
              </tr>
              <tr>
                <th class="text-left">Core ITS Network Scan</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails.networkScan) }}</td>
              </tr>
              <tr>
                <th class="text-left">MSA</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails.isMsa) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Payment Details Section -->
    <v-expansion-panels v-model="paymentPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">payments</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.payment_details') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-table density="compact">
            <tbody>
              <tr>
                <th class="text-left">Pricing Category</th>
                <td class="text-left">{{ formatValue(paymentDetails.pricingCategoryCode) }}</td>
              </tr>
              <tr>
                <th class="text-left">Minimum Commitment</th>
                <td class="text-left">{{ formatCurrency(paymentDetails.minimumCommitmentAmount) }}</td>
              </tr>
              <tr>
                <th class="text-left">Payment Mode</th>
                <td class="text-left">{{ formatValue(paymentDetails.paymentMode) }}</td>
              </tr>
              <tr v-if="paymentDetails.paymentJustification">
                <th class="text-left">Payment Justification</th>
                <td class="text-left">{{ formatValue(paymentDetails.paymentJustification) }}</td>
              </tr>
              <tr v-if="paymentDetails.leaseTermInMonth">
                <th class="text-left">Lease Term (Months)</th>
                <td class="text-left">{{ formatValue(paymentDetails.leaseTermInMonth) }}</td>
              </tr>
              <tr v-if="paymentDetails.typeOfContract">
                <th class="text-left">Type of Contract</th>
                <td class="text-left">{{ formatValue(paymentDetails.typeOfContract) }}</td>
              </tr>
              <tr v-if="paymentDetails.billingPeriod">
                <th class="text-left">Billing Period</th>
                <td class="text-left">{{ formatValue(paymentDetails.billingPeriod) }}</td>
              </tr>
              <tr>
                <th class="text-left">Supplier Type</th>
                <td class="text-left">{{ formatValue(paymentDetails.supplierType) }}</td>
              </tr>
              <tr>
                <th class="text-left">Implementation</th>
                <td class="text-left">{{ formatValue(paymentDetails.implementation) }}</td>
              </tr>
              <tr>
                <th class="text-left">Foreign Buyout/Trade Up</th>
                <td class="text-left">{{ formatValue(paymentDetails.foreignBuyout) }}</td>
              </tr>
              <tr>
                <th class="text-left">Proposal Date</th>
                <td class="text-left">{{ formatDate(paymentDetails.proposalDate) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Equipment Details Section -->
    <v-expansion-panels v-model="equipmentPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">devices</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.equipments') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <!-- Equipment Basic Info -->
          <h3 class="text-subtitle-1 mb-2">Equipment Information</h3>
          <v-table density="compact" class="mb-4">
            <tbody>
              <tr v-if="equipmentDetails?.equipment">
                <th class="text-left">Selling Price</th>
                <td class="text-left">{{ formatCurrency(equipmentDetails.equipment.sellingPrice) }}</td>
              </tr>
              <tr v-if="equipmentDetails?.equipment">
                <th class="text-left">MSRP</th>
                <td class="text-left">{{ formatCurrency(equipmentDetails.equipment.msrp) }}</td>
              </tr>
              <tr v-if="equipmentDetails?.equipment">
                <th class="text-left">% of MSRP</th>
                <td class="text-left">{{ equipmentDetails.equipment.percentOfMsrp }}</td>
              </tr>
              <tr>
                <th class="text-left">Potential Competition</th>
                <td class="text-left">{{ formatArray(equipmentDetails?.competitionName) }}</td>
              </tr>
              <tr>
                <th class="text-left">MFP Incumbent</th>
                <td class="text-left">{{ equipmentDetails?.mfpIncumbent || 'N/A' }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- Installation Details -->
          <h3 class="text-subtitle-1 mb-2">Installation Details</h3>
          <v-table density="compact" class="mb-4">
            <tbody>
              <tr>
                <th class="text-left">Potential Units</th>
                <td class="text-left">{{ equipmentDetails?.potentialUnits || 'N/A' }}</td>
              </tr>
              <tr>
                <th class="text-left">Installation Date</th>
                <td class="text-left">{{ formatDate(equipmentDetails?.installationDate) }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- Strategy Information -->
          <h3 class="text-subtitle-1 mb-2">Strategy Information</h3>
          <v-table density="compact" class="mb-4">
            <tbody>
              <tr>
                <th class="text-left">Potential Units</th>
                <td class="text-left">{{ formatValue(equipmentDetails.potentialUnitCount) }}</td>
              </tr>
              <tr>
                <th class="text-left">Installation Date</th>
                <td class="text-left">{{ formatDate(equipmentDetails.installationDate) }}</td>
              </tr>
              <tr>
                <th class="text-left">Special Considerations</th>
                <td class="text-left">{{ formatValue(equipmentDetails.specialConsideration) }}</td>
              </tr>
              <tr>
                <th class="text-left">Hardware Pricing Strategy</th>
                <td class="text-left">{{ formatValue(equipmentDetails.hardwarePricingStrategy) }}</td>
              </tr>
              <tr>
                <th class="text-left">Software Sales Strategy</th>
                <td class="text-left">{{ formatValue(equipmentDetails.softwareSalesStrategy) }}</td>
              </tr>
              <tr>
                <th class="text-left">Current Incumbent</th>
                <td class="text-left">{{ formatValue(equipmentDetails.currentIncumbent) }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- Equipment Distribution Table -->
          <h3 class="text-subtitle-1 mb-2">Equipment Distribution</h3>

          <v-table density="compact" v-if="equipmentDetails?.tableData">
            <thead>
              <tr>
                <th class="text-left">Category</th>
                <th class="text-left">Units</th>
                <th v-for="(vendor, index) in ['Canon BSS', 'Canon Dealer', 'Xerox', 'Ricoh', 'Sharp', 'Konica M.']" :key="index" class="text-left">
                  {{ vendor }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in equipmentDetails.mfpIncumbents" :key="index">
                <td>{{ row.category }}</td>
                <td>{{ row.units }}</td>
                <td v-for="(value, vIndex) in row.values" :key="vIndex">
                  {{ formatPercentage(value) }}
                </td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Worksheet Details Section -->
    <v-expansion-panels v-model="worksheetPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">description</v-icon>
            <span class="text-h6">{{ t('page.sales_request_form.tabs.worksheets') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div v-if="hierarchicalWorksheetData?.length > 0">
            <!-- Main products table with expandable rows -->
            <v-table density="compact" class="mb-4">
              <thead>
                <tr>
                  <th width="40" class="text-center"></th>
                  <th class="text-left">Item ID</th>
                  <th class="text-left">Display Name</th>
                  <th class="text-left">DSD Quantity</th>
                  <th class="text-left">Dealer Quantity</th>
                  <th class="text-left">MSRP</th>
                  <th class="text-left">Requested Price</th>
                  <th class="text-left">Type</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="(mainUnit, mainIndex) in hierarchicalWorksheetData" :key="mainIndex">
                  <!-- Main Unit Row -->
                  <tr class="main-unit-row">
                    <td class="text-center">
                      <v-icon
                        v-if="mainUnit.subProducts && mainUnit.subProducts.length > 0"
                        size="small"
                        :color="mainUnit.isSolution === 'Y' ? 'blue' : 'green'"
                      >
                        expand_less
                      </v-icon>
                    </td>
                    <td class="font-weight-bold">{{ mainUnit.itemId }}</td>
                    <td class="font-weight-bold" :class="{ 'text-blue': mainUnit.isSolution === 'Y', 'text-green': mainUnit.isSolution === 'N' }">
                      {{ mainUnit.displayName }}
                    </td>
                    <td class="font-weight-bold">{{ mainUnit.dsdQuantity || 0 }}</td>
                    <td class="font-weight-bold">{{ mainUnit.dealerIt || 0 }}</td>
                    <td class="font-weight-bold">{{ formatCurrency(mainUnit.msrp) }}</td>
                    <td class="font-weight-bold">{{ formatCurrency(mainUnit.requestedSellingPrice) }}</td>
                    <td>
                      <v-chip
                        size="small"
                        :color="mainUnit.isSolution === 'Y' ? 'blue' : 'green'"
                        variant="tonal"
                      >
                        {{ mainUnit.isSolution === 'Y' ? 'Solution' : 'Hardware' }}
                      </v-chip>
                    </td>
                  </tr>

                  <!-- Sub Units Rows (Always shown since _expanded is true) -->
                  <template v-if="mainUnit.subProducts && mainUnit.subProducts.length > 0">
                    <tr
                      v-for="subUnit in mainUnit.subProducts"
                      :key="subUnit.requestItemId"
                      class="sub-unit-row"
                    >
                      <td class="text-center">
                        <v-icon size="small" class="text-grey">subdirectory_arrow_right</v-icon>
                      </td>
                      <td class="pl-4 text-grey-darken-1">{{ subUnit.itemId }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.displayName }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.dsdQuantity || 0 }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.dealerIt || 0 }}</td>
                      <td class="text-grey-darken-1">{{ formatCurrency(subUnit.msrp) }}</td>
                      <td class="text-grey-darken-1">{{ formatCurrency(subUnit.requestedSellingPrice) }}</td>
                      <td>
                        <v-chip
                          size="small"
                          :color="subUnit.isSolution === 'Y' ? 'blue' : 'green'"
                          variant="outlined"
                        >
                          {{ subUnit.isSolution === 'Y' ? 'Solution' : 'Hardware' }}
                        </v-chip>
                      </td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </v-table>
          </div>
          <v-alert v-else type="info" class="mt-2">
            No worksheet data available
          </v-alert>

          <!-- Worksheet Summary Section -->
          <div v-if="hierarchicalWorksheetData && hierarchicalWorksheetData.length > 0" class="mt-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <v-icon class="mr-2">summarize</v-icon>
              Worksheet Summary
            </h3>

            <!-- Solution Items Summary -->
            <div class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-blue">Solution Items Totals</h4>
              <v-row>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">DSD Quantity</div>
                    <div class="text-h6 text-blue">{{ solutionTotals.dsdQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">Dealer Quantity</div>
                    <div class="text-h6 text-blue">{{ solutionTotals.dealerQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">MSRP</div>
                    <div class="text-h6 text-blue">{{ formatCurrency(solutionTotals.msrp) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">Requested Price</div>
                    <div class="text-h6 text-blue">{{ formatCurrency(solutionTotals.requestedPrice) }}</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>

            <!-- Hardware Items Summary -->
            <div class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-green">Hardware Items Totals</h4>
              <v-row>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">DSD Quantity</div>
                    <div class="text-h6 text-green">{{ hardwareTotals.dsdQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">Dealer Quantity</div>
                    <div class="text-h6 text-green">{{ hardwareTotals.dealerQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">MSRP</div>
                    <div class="text-h6 text-green">{{ formatCurrency(hardwareTotals.msrp) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">Requested Price</div>
                    <div class="text-h6 text-green">{{ formatCurrency(hardwareTotals.requestedPrice) }}</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>

            <!-- Grand Totals -->
            <div class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-primary">Grand Totals (All Items)</h4>
              <v-row>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">DSD Quantity</div>
                    <div class="text-h5 font-weight-bold">{{ grandTotals.dsdQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">Dealer Quantity</div>
                    <div class="text-h5 font-weight-bold">{{ grandTotals.dealerQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">MSRP</div>
                    <div class="text-h5 font-weight-bold">{{ formatCurrency(grandTotals.msrp) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">Requested Price</div>
                    <div class="text-h5 font-weight-bold">{{ formatCurrency(grandTotals.requestedPrice) }}</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Service Request Details Section -->
    <v-expansion-panels v-model="serviceRequestPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">assignment</v-icon>
            <span class="text-h6">Service Request Details</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div v-if="isServiceRequestLoading" class="text-center pa-4">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <div class="mt-2">Loading service request data...</div>
          </div>
          <v-alert v-else-if="serviceRequestLoadError" type="error" class="ma-4">
            {{ serviceRequestLoadError }}
          </v-alert>
          <ServiceRequestPreview
            v-else
            :service-request-data="serviceRequestData"
          />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-container>
</template>

<style scoped>
.v-expansion-panel-title {
  background-color: #f5f5f5;
}

.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.main-unit-row {
  background-color: #f5f5f5;
  border-left: 4px solid #4caf50;
}

.main-unit-row.solution {
  border-left-color: #2196f3;
}

.sub-unit-row {
  background-color: #fafafa;
  border-left: 2px solid #e0e0e0;
}

.sub-unit-row td {
  font-size: 0.875rem;
  color: #6c757d;
}

.text-blue {
  color: #2196f3 !important;
}

.text-green {
  color: #4caf50 !important;
}

.rotate-45 {
  transform: rotate(45deg);
  transition: transform 0.2s;
}
</style>









