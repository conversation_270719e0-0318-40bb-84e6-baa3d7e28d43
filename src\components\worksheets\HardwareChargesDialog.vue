<script setup lang="ts">
/**
 * @file Dialog for adding or editing charges for a hardware worksheet item.
 */
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { WorkSheetProduct } from '@/lib/common/types';
import { getHardwareChargesRates, saveVpRate } from '@/lib/api';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';

interface Props {
  modelValue: boolean; // v-model visibility
  item: WorkSheetProduct | null; // target hardware item
}
const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'saved'): void;
}>();

const { t } = useI18n();

// Dropdown selections
const portfolio = ref<string>('');
const servicePack = ref<string>('');

// Portfolio options
const portfolioOptions = ref<{ title: string; value: number }[]>([]);

// Service Pack options
const servicePackOptions = ref<{ title: string; value: number }[]>([]);

const loadPortfolioOptions = async () => {
  try {
    const lov = await getLov(LovCategories.PORTFOLIO);
    portfolioOptions.value = lov.map((item: LovItem) => ({ title: item.description, value: Number(item.lookupCode) }));
  } catch (err) {
    console.error('Error loading portfolio values', err);
  }
};

const loadServicePackOptions = async () => {
  try {
    const lov = await getLov(LovCategories.SERVICE_VALUE_PACK);
    servicePackOptions.value = lov.map((item: LovItem) => ({ title: item.value, value: Number(item.lookupCode) }));
  } catch (err) {
    console.error('Error loading service pack values', err);
  }
};

watch(() => props.modelValue, (val) => {
  if (val) {
    // dialog opened
    if (portfolioOptions.value.length === 0) loadPortfolioOptions();
    if (servicePackOptions.value.length === 0) loadServicePackOptions();
  } else {
    // reset selections and rate fields when closed
    portfolio.value = '';
    servicePack.value = '';
    rates.value = {
      vpRateId: null,
      blackAndWhite: 0,
      colour: 0,
      iprc: 0,
      minimumBaseAmount: 0,
      minimumBaseVolume: 0
    };
  }
});

// Rates returned from API
interface Rates {
  vpRateId: number;
  blackAndWhite: number;
  colour: number;
  iprc: number;
  minimumBaseAmount: number;
  minimumBaseVolume: number;
}

const rates = ref<Rates>({
  vpRateId: null,
  blackAndWhite: 0,
  colour: 0,
  iprc: 0,
  minimumBaseAmount: 0,
  minimumBaseVolume: 0
});

// Determine if we should show Add or Update
const isNew = computed(() => {
  return !!!rates.value.vpRateId;
});

// Fetch rates whenever dropdowns change
const formatCurrency = (value: number | undefined) => {
  if (value === undefined || value === null) return '$0.00';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

const fetchRates = async () => {
  if (!props.item || !portfolio.value || !servicePack.value) return;
  try {
    const response = await getHardwareChargesRates(
      props.item.itemId?.toString() || props.item.id.toString(),
      servicePack.value,
      portfolio.value,
    );
    // Map API response to rates fields using payload-compatible keys
    const data = response.data || {};
    rates.value.blackAndWhite = data.blackAndWhite ?? 0;
    rates.value.colour = data.colorValue ?? 0;
    rates.value.iprc = data.oversizedValue ?? 0;
    rates.value.minimumBaseAmount = data.minimumBase ?? 0;
    rates.value.minimumBaseVolume = data.minimumVolume ?? 0;
    rates.value.vpRateId = data.vpRateId == 0 ? null : data.vpRateId;
  } catch (error) {
    console.error('Error fetching rates:', error);
  }
};

watch([portfolio, servicePack, () => props.item], fetchRates);

// Close dialog
const close = () => emit('update:modelValue', false);

const save = async () => {
    if (!props.item || !portfolio.value || !servicePack.value) {
    console.warn('Missing required fields');
    return;
  }

  const payload = {
    vpRateId: rates.value.vpRateId ?? null,
    itemId: props.item.itemId || props.item.id,
    servicePackId: Number(servicePack.value),
    portfolioId: Number(portfolio.value),
    blackAndWhite: rates.value.blackAndWhite,
    colorValue: rates.value.colour,
    oversizedValue: rates.value.iprc,
    minimumBase: rates.value.minimumBaseAmount,
    minimumBaseUnit: 'copies',
    minimumVolume: rates.value.minimumBaseVolume,
    isActive: 'Y'
  };


  try {
    await saveVpRate(payload);
    emit('saved');
    close();
  } catch (err) {
    console.error('Error saving VP Rate:', err);
  }
};
</script>

<template>
  <v-dialog :model-value="modelValue" max-width="600">
    <v-card>
      <v-card-title class="text-h6">
        {{ isNew ? t('page.worksheets_management.dialog.add_charges') : t('page.worksheets_management.dialog.edit_charges') }}
      </v-card-title>

      <v-card-text>
        <div v-if="item" class="mb-4">
          <div class="text-subtitle-2 font-weight-medium">{{ item.displayName }}</div>
          <div class="text-body-2 grey--text">{{ item.modelName }} • {{ item.itemNumber }}</div>
          <div class="text-body-2 mt-1">{{ t('page.worksheets_management.dialog.msrp') }}: {{ formatCurrency(item.msrp as any) }}</div>
        </div>

        <v-row class="mb-4">
          <v-col cols="12" sm="6">
            <v-select
              v-model="portfolio"
              :items="portfolioOptions"
              :label="t('page.worksheets_management.dialog.portfolio')"
              clearable
            />
          </v-col>
          <v-col cols="12" sm="6">
            <v-select
              v-model="servicePack"

              :items="servicePackOptions"
              :label="t('page.worksheets_management.dialog.service_value_pack')"
              clearable
            />
          </v-col>
        </v-row>

        <v-row>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.blackAndWhite" :label="t('page.worksheets_management.dialog.bw')" type="number" />
          </v-col>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.colour" :label="t('page.worksheets_management.dialog.colour')" type="number" />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.iprc" :label="t('page.worksheets_management.dialog.iprc')" type="number" />
          </v-col>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.minimumBaseAmount" :label="t('page.worksheets_management.dialog.minimum_base_amount')" type="number" />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.minimumBaseVolume" :label="t('page.worksheets_management.dialog.minimum_base_volume')" type="number" />
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions class="justify-end">
        <v-btn text @click="close">{{ t('common.cancel') }}</v-btn>
        <v-btn color="primary" @click="save">
          {{ isNew ? t('common.add') : t('common.update') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
</style>
