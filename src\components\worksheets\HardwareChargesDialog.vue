<script setup lang="ts">
/**
 * @file Dialog for adding or editing charges for a hardware worksheet item.
 */
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { WorkSheetProduct } from '@/lib/common/types';

// Extend worksheet product to include portfolio/service pack info coming from VP Rate list
interface ProductWithRate extends WorkSheetProduct {
  vpRateId?: number;
  blackAndWhite?: number;
  colour?: number;
  colorValue?: number;
  oversizedValue?: number;
  minimumBase?: number;
  minimumBaseAmount?: number;
  minimumVolume?: number;
  minimumBaseVolume?: number;

  portfolioId?: number;
  portfolioValue?: string;
  servicePackId?: number;
  servicePackValue?: string;
  isDealerAcceptedSr?: string; // 'Y' or 'N'
}
import { saveVpRate, updateVpRate } from '@/lib/api';


interface Props {
  modelValue: boolean; // v-model visibility
  item: ProductWithRate | null; // target hardware item
}
const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'saved'): void;
}>();

const { t } = useI18n();



// Rates returned from API
interface Rates {
  vpRateId: number | null;
  blackAndWhite: number;
  colour: number;
  iprc: number;
  minimumBaseAmount: number;
  minimumBaseVolume: number;
}

// Accessory Include (CPC) checkbox state
const accessoryInc = ref<boolean>(false);

const rates = ref<Rates>({
  vpRateId: null,
  blackAndWhite: 0,
  colour: 0,
  iprc: 0,
  minimumBaseAmount: 0,
  minimumBaseVolume: 0
});

// Determine if we should show Add or Update
const isNew = computed(() => {
  return !!!rates.value.vpRateId;
});

// Initialize rates from the passed item
watch(
  () => props.item,
  (val) => {
    if (!val) return;
    accessoryInc.value = (val as any).isDealerAcceptedSr === 'Y';
    rates.value = {
      vpRateId: val.vpRateId || null,
      blackAndWhite: val.blackAndWhite ?? 0,
      colour: val.colorValue ?? 0,
      iprc: val.oversizedValue ?? 0,
      minimumBaseAmount: val.minimumBaseAmount ?? val.minimumBase ?? 0,
      minimumBaseVolume: val.minimumBaseVolume ?? val.minimumVolume ?? 0,
    };
  },
  { immediate: true }
);



// Close dialog
const close = () => emit('update:modelValue', false);

const save = async () => {
    if (!props.item) {
    console.warn('Missing item');
    return;
  }

  const payload = {
    vpRateId: rates.value.vpRateId ?? null,
    itemId: props.item.itemId || props.item.id,
    servicePackId: props.item.servicePackId,
    portfolioId: props.item.portfolioId,
    blackAndWhite: rates.value.blackAndWhite,
    colorValue: rates.value.colour,
    oversizedValue: rates.value.iprc,
    minimumBase: rates.value.minimumBaseAmount,
    minimumBaseUnit: 'copies',
    minimumVolume: rates.value.minimumBaseVolume,
    isActive: 'Y',
    isDealerAcceptedSr: accessoryInc.value ? 'Y' : 'N'
  };


  try {
    if (isNew.value) {
      await saveVpRate(payload);
    } else {
      await updateVpRate(payload);
    }
    emit('saved');
    close();
  } catch (err) {
    console.error('Error saving VP Rate:', err);
  }
};
</script>

<template>
  <v-dialog :model-value="modelValue" max-width="600">
    <v-card>
      <v-card-title class="text-h6">
        {{ isNew ? t('page.worksheets_management.dialog.add_charges') : t('page.worksheets_management.dialog.edit_charges') }}
      </v-card-title>

      <v-card-text>
        <div v-if="item" class="mb-4">
          <div class="text-subtitle-2 font-weight-medium">{{ item.displayName }}</div>

        </div>

        <div class="mb-4">
          <div class="text-body-2">{{ t('page.worksheets_management.dialog.portfolio') }}: <strong>{{ item!.portfolioValue }}</strong></div>
          <div class="text-body-2">{{ t('page.worksheets_management.dialog.service_value_pack') }}: <strong>{{ item!.servicePackValue }}</strong></div>
        </div>

        <!-- Accessory Include (CPC) checkbox -->
        <v-row class="mb-2">
          <v-col cols="12">
            <v-checkbox
              v-model="accessoryInc"
              label="Accessory Inc.(CPC)"
              :true-value="true"
              :false-value="false"
            ></v-checkbox>
          </v-col>
        </v-row>

        <v-row>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.blackAndWhite" :label="t('page.worksheets_management.dialog.bw')" type="number" />
          </v-col>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.colour" :label="t('page.worksheets_management.dialog.colour')" type="number" />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.iprc" :label="t('page.worksheets_management.dialog.iprc')" type="number" />
          </v-col>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.minimumBaseAmount" :label="t('page.worksheets_management.dialog.minimum_base_amount')" type="number" />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" sm="6">
            <v-text-field v-model.number="rates.minimumBaseVolume" :label="t('page.worksheets_management.dialog.minimum_base_volume')" type="number" />
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions class="justify-end">
        <v-btn variant="text" @click="close">{{ t('common.cancel') }}</v-btn>
        <v-btn color="primary" @click="save">
          {{ isNew ? t('common.add') : t('common.update') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
</style>
