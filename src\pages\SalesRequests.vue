<script setup lang="ts">
/**
 * @file Sales Requests page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import DataTable from '@/components/common/DataTable.vue';
import { useAppStore } from '@/stores/AppStore';
import actionPermissionChannel from '@/composables/auth/actionPermissionChannel';
import { Channel_Action } from '@/lib/common/types';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();
const router = useRouter();

// Add language support
const { t } = useI18n();

// Table data
import { getSalesRequests, type SalesRequestDto } from '@/services/salesRequestService';
import { getDsdRequestStatusText, DsdRequestStatus } from '@/enums/DsdRequestStatus';
import StatusBadge from '@/components/core/StatusBadge.vue';

const loading = ref(false);
const error = ref('');
const headers = ref([
    { title: t('page.sales_requests.table.header.id'), key: 'requestNumber' },
    { title: t('page.sales_requests.table.header.customer'), key: 'customerId' },
    // { title: t('page.sales_requests.table.header.product'), key: 'product' },
    { title: t('page.sales_requests.table.header.status'), key: 'dsdRequestStatus' },
    { title: t('page.sales_requests.table.header.date'), key: 'createdAt' },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref<SalesRequestDto[]>([]);

// Handle table actions
const handleAction = ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
        // Handle view action
        if (item && item.requestId) {
            router.push({ name: 'pageViewSalesRequest', params: { id: item.requestId } });
        } else {
            // Optionally show an error or warning
            console.error('Invalid or missing sales request ID:', item);
        }
    } 
    else if (action === 'edit') {
        if (item && item.requestId) {
            router.push({ name: 'pageEditSalesRequest', params: { id: item.requestId } });
        } else {
            // Optionally show an error or warning
            console.error('Invalid or missing sales request ID:', item);
        }
    }
    else if (action === 'approve') {
        // Handle approve action
        router.push({ name: 'Quote', params: { id: item.id } });
        console.log('Approve request:', item);
    } else if (action === 'reject') {
        // Handle reject action
        router.push({ name: 'pageServiceApproval'});
        console.log('Reject request:', item);
    }
    else if (action === 'pageServiceRequestForm') {
        // Handle reject action
        router.push({ name: 'pageServiceRequestForm', params: { id: item.id } });
        console.log('Reject request:', item);
    } else if (action === 'approve_pricing') {
        // Handle approve pricing action
        router.push({ name: 'Quote', params: { id: item.requestId } });
     
        console.log('Approve pricing for request:', item);
    }
};

// Load data
onMounted(async () => {
    appStore.stopPageLoader();
    loading.value = true;
    error.value = '';
    try {
        const data = await getSalesRequests();
        items.value = data;
    } catch (e) {
        error.value = t('common.error_loading_data') || 'Failed to load sales requests.';
    } finally {
        loading.value = false;
    }
});
</script>

<template>
    <div class="pa-4">
        <div class="d-flex justify-space-between align-center mb-4">
            <h1>{{ t('page.sales_requests.title') }}</h1>
            <v-btn v-if="actionPermissionChannel( Channel_Action.SALES_OPERATIONS )"
                color="primary" 
                prepend-icon="add" 
                :to="{ name: 'pageNewSalesRequest' }"
            >
                {{ t('page.sales_requests.button.new_request') }}
            </v-btn>
        </div>
        
        <v-card class="mt-4">
 
            
            <!-- <v-card-text> -->
                <DataTable 
                    :headers="headers" 
                    :items="items" 
                    :loading="loading"
                    @action="handleAction"
                >
                <template v-slot:item.dsdRequestStatus="{ item }">
                        <!-- <v-chip
                            :color="item.dsdRequestStatus === DsdRequestStatus.APPROVED ? 'success' : item.dsdRequestStatus === DsdRequestStatus.MANAGER_APPROVAL_PENDING ? 'primary' : 'warning'"
                            size="small"
                        >
                            {{ getDsdRequestStatusText(item.dsdRequestStatus) }}
                        </v-chip> -->
                        <StatusBadge :status="item.dsdRequestStatus || 0" />
                    </template>
                    <template v-slot:item.actions="{ item }">
                        <v-menu>
                            <template v-slot:activator="{ props }">
                                <v-btn
                                    icon="more_vert"
                                    variant="text"
                                    size="small"
                                    v-bind="props"
                                ></v-btn>
                            </template>
                            <v-list>
                                <v-list-item
                                    prepend-icon="visibility"
                                    title="View"
                                    @click="handleAction({ action: 'view', item })"
                                ></v-list-item>
                                <v-list-item
                                    v-if="item.dsdRequestStatus === DsdRequestStatus.DRAFT && actionPermissionChannel(Channel_Action.SALES_OPERATIONS)"
                                    prepend-icon="edit"
                                    title="Edit"
                                    @click="handleAction({ action: 'edit', item })"
                                ></v-list-item>
                        
                                <v-list-item
                                    prepend-icon="attach_money"
                                    title="Approve Pricing"
                                    @click="handleAction({ action: 'approve_pricing', item })"
                                ></v-list-item>
                    

                                   <v-list-item
                                    
                                    prepend-icon="cancel"
                                    title="Fill Service Form"
                                    @click="handleAction({ action: 'pageServiceRequestForm', item })"
                                ></v-list-item>
                            </v-list>
                        </v-menu>
                    </template>
                    <template v-slot:item.createdAt="{ item }">
                        <span>{{ new Date(item.createdAt).toLocaleDateString(undefined, { year: 'numeric', month: '2-digit', day: '2-digit' }) }}</span>
                    </template>
                    <template v-slot:item.customerId="{ item }">
                        <span>{{ !!item.customer ? item.customer.displayName : item.customerId }}</span>
                    </template>
                </DataTable>
            <!-- </v-card-text> -->
        </v-card>
    </div>
</template>
