<script setup lang="ts">
	/**
	 * @file Header component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

	import { ref, watch } from 'vue';
	import { useDisplay  } from 'vuetify';
    import { useAppStore } from '@/stores/AppStore';
    import { useUserStore } from '@/stores/UserStore';
	import UserMenu from './UserMenu.vue';
    import actionPermissions from '@/composables/auth/actionPermission';
    import { Channel_Action, CN_Action, UserRole } from '@/lib/common/types';
    import { useI18n } from 'vue-i18n';
import { useInternalUserStore } from '../../stores/InternalUserStore';
import actionPermissionChannel from '@/composables/auth/actionPermissionChannel';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
	const appStore  = useAppStore();
    const userStore  = useUserStore();
	const userInternalStore = useInternalUserStore();

    // Add language support.
    const { t } = useI18n();

	// Data
	const { mdAndUp } = useDisplay()
	const drawer = ref( true );
	const appTitle = t('app.title');
	const appIcon = import.meta.env.VITE_APP_ICON;

	// Watches for when the page is viewed in mobile resolution and disables the drawer if left open.
	watch( mdAndUp, async (new_mdAndUp, old_mdAndUp ) =>
	{
		if ( new_mdAndUp )
		{
			drawer.value = false;
		}
	});
</script>

<template>
	<v-app-bar color="header" elevation="2" height="52" order="0" app>
		<template  v-slot:prepend v-if="userStore.authenticated">
			<v-app-bar-nav-icon color="onHeader" @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
		</template>

		<v-app-bar-title >
			<RouterLink :to="{ name : 'pageHome' }" class="no-link header-title">
				<img width="74"
					src="https://cci0it0general.blob.core.windows.net/public/image/logo/Canon-Trans-Red-100_25.png">
				<span>{{t('app.title')}}</span>
				<v-icon v-if="appIcon" color="onHeader" :icon="appIcon" size="23px" end></v-icon>
			</RouterLink>
		</v-app-bar-title>

		<v-spacer></v-spacer>

		<!-- -------------- -->
		<!-- TOP NAVIGATION -->
		<!-- -------------- -->
		<div v-if="userStore.authenticated && (actionPermissions( CN_Action.ADMIN ) || userInternalStore.approver)" class="d-none d-md-inline-block">
			<v-btn :to="{ name : 'pageHome' }" exact>{{ t( 'component.appheader.button.home.label' ) }}</v-btn>
		</div>

		<v-btn v-if="actionPermissions( CN_Action.ADMIN )" :to="{ name : 'pageTroubleshoot' }" exact icon
			size="small" class="ml-3">
			<v-icon color="onHeader" size="24" icon="code"></v-icon>
		</v-btn>

		<user-menu />
	</v-app-bar>

	<v-progress-linear v-if="appStore.pageLoader" indeterminate
		style="top: 52px !important; position: fixed;"></v-progress-linear>

	<!-- ------------------>
	<!-- SIDE NAVIGATION -->
	<!-- --------------- -->
	<v-navigation-drawer v-model="drawer" location="left" disable-resize-watcher
		color="navDrawer" order="1" v-if="userStore.authenticated">

		<v-list bg-color="navDrawer" active-class="navDrawerActive">
			<v-list-item  :to="{ name: 'pageHome' }" exact>
				<template v-slot:prepend dence >
					<v-icon>home</v-icon>
				  </template>
				<v-list-item-title >{{ t('component.appheader.button.home.label') }}</v-list-item-title>
			</v-list-item>

			<!-- <v-list-item prepend-icon="manage_accounts" :to="{ name: 'pageRoleManagement' }" exact>
				<v-list-item-title>{{ t('component.appheader.button.role_mgmt.label') }}</v-list-item-title>
			</v-list-item> -->

	

				<v-list-group v-if="
			actionPermissionChannel( Channel_Action.SALES_OPERATIONS ) || actionPermissionChannel( Channel_Action.SALES_MANAGER_REGIONAL_LEADERSHIP ) || actionPermissionChannel( Channel_Action.SALES_MANAGEMENT )" prepend-icon="all_inbox" value="false">
				<template v-slot:activator="{ props }">
					<v-list-item v-bind="props">
						<v-list-item-title>{{ t('component.appheader.button.sales_request_listing.label') }}</v-list-item-title>
					</v-list-item>
				</template>
				<v-list-item v-if="actionPermissionChannel(Channel_Action.SALES_MANAGER_REGIONAL_LEADERSHIP )" prepend-icon="supervisor_account" :to="{ name: 'allSalesRequests' }" exact>
					<v-list-item-title>{{ t('All Requests') }}</v-list-item-title>
				</v-list-item>
				<v-list-item v-if="actionPermissionChannel(Channel_Action.SALES_OPERATIONS) " prepend-icon="account_box" :to="{ name: 'mySalesRequests' }" exact >
					<v-list-item-title>{{ t('My Requests') }}</v-list-item-title>
				</v-list-item>

			</v-list-group>
					<!-- <v-list-item  v-if="actionPermissionChannel( Channel_Action.SALES_OPERATIONS )" prepend-icon="shopping_cart" :to="{ name: 'pageSalesRequests' }" exact>
				<v-list-item-title>{{ t('component.appheader.button.sales_request_listing.label') }}</v-list-item-title>
			</v-list-item>
			<v-list-item v-if="userInternalStore.roles?.includes(UserRole.SALES_REP)" prepend-icon="attach_money" :to="{ name: 'pagePriceDesk' }" exact>
				<v-list-item-title>{{ t('component.appheader.button.price_desk_listing.label') }}</v-list-item-title>
			</v-list-item> -->
				<v-list-item v-if="userInternalStore.roles?.includes(UserRole.SERVICE_DESK_ANALYST)" prepend-icon="attach_money" :to="{ name: 'pagePriceDesk' }" exact>
				<v-list-item-title>{{ t('component.appheader.button.price_desk_listing.label') }}</v-list-item-title>
			</v-list-item>
			
		<v-list-group value="Configuration"  v-if="actionPermissions( CN_Action.ADMIN ) || userInternalStore.roles?.includes(UserRole.ADMIN)">
				<!-- Remove userInternalStore.roles when needs to be deployed at Prod -->
				<template v-slot:activator="{ props }">
					<v-list-item
						v-bind="props"
						prepend-icon="settings"
						:title="t('component.appheader.button.configuration.label')"
					></v-list-item>
				</template>
				<v-list-item
					prepend-icon="group"
					:title="t('component.appheader.button.user_access.label')"
					:to="{ name: 'pageUserAccess' }"
					exact
				>
				</v-list-item>
					<v-list-item
					prepend-icon="dataset"
					:title="t('Worksheets Data')"
					:to="{ name: 'worksheetsData' }"
					exact
				>
				</v-list-item>
			</v-list-group>
	
		</v-list>
	</v-navigation-drawer>
</template>

<style lang="scss">
	.navDrawerActive
	{
		background : rgb(var(--v-theme-navDrawer));
    	color : rgba(var(--v-theme-onNavDrawer));
	}

	.header-title
	{
		display : flex;
		align-items : center;

		img 
		{
			margin-right : 20px;
		}

		span
		{
			font-size : 18px !important;
			line-height : 18px !important;
			margin-top: 2px;
			cursor : pointer;
            font-weight: 500;
		}

		i
		{
			justify-content : start;
            margin-top: 1px;
            margin-left: 2px;
        }
	}

    .v-toolbar-title
    {
        flex: unset;
    }
	.v-list-item__spacer{
		width: 24px !important;
	}
	.v-list-group {
		--list-indent-size: 16px;
		--parent-padding: var(--indent-padding);
		--prepend-width: 5px;
	}
</style>
