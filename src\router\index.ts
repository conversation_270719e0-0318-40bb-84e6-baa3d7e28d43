/**
 * @file Router configuration for application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { createRouter, createWebHistory } from 'vue-router';
import { CanonAuthGuard } from '@/lib/canonAuth';
import { useAppStore } from '@/stores/AppStore';
import { RoleEnforcement } from '@/lib/common/types';

/**
 * ----
 * Main
 * ----
 */

// Defined routes for vue-router.
const routes  =
[
	{
		path: '/',
		name: 'viewMain',
		component: () => import( "@/views/MainView.vue" ),
		meta:
		{
			secure: false
		},
		children :
		[
			{
				path: '',
				name: 'pageHome',
				component: () => import( "@/pages/Home.vue" ),
				meta:
				{
					secure: false
				}
			},
            {
				path: '/tbshoot',
				name: 'pageTroubleshoot',
				component: () => import( "@/pages/Troubleshoot.vue" ),
				meta:
				{
					secure: true
				}
			},
			{
				path: '/worksheets',
				name: 'worksheetsData',
				component: () => import("@/pages/WorksSheetsManagement.vue"),
				meta: {
					secure: true
				}
			},
			{
				path: '/sales-requests',
				name: 'allSalesRequests',
				component: () => import("@/pages/SalesRequests.vue"),
				meta: {
					secure: true
				}
			},
				{
				path: '/my-requests',
				name: 'mySalesRequests',
				component: () => import("@/pages/SalesRequests.vue"),
				meta: {
					secure: true
				}
			},
			{
                path: '/sales-requests/new',
                name: 'pageNewSalesRequest',
                component: () => import("@/pages/SalesRequestForm.vue"),
                meta: {
                    secure: true
                }
            },
            {
                path: '/sales-requests/:id',
                name: 'pageEditSalesRequest',
                component: () => import("@/pages/SalesRequestForm.vue"),
                props: route => ({ id: route.params.id, viewOnly: false }),
                meta: {
                    secure: true
                }
            },
            {
                path: '/sales-requests/:id/view',
                name: 'pageViewSalesRequest',
                component: () => import("@/pages/SalesRequestForm.vue"),
                props: route => ({ id: route.params.id, viewOnly: true }),
                meta: {
                    secure: true
                }
            },
            {
                path: '/service-request-form/:id?',
                name: 'pageServiceRequestForm',     
                component: () => import("@/pages/ServiceRequestFormPage.vue"),
                props: true, // Automatically pass route params as props to the component
                meta: {
                    secure: true
                }
            },
			{
				path: '/quote/:id',
				name: 'Quote',
				component: () => import("@/pages/QuotePage.vue"),
				props: true,
				meta: {
					secure: true
				}
			},
			{
				path: '/service-approval',
				name: 'pageServiceApproval',
				component: () => import("@/pages/ServiceApproval.vue"),
				meta: {
					secure: true
				}
			},
			{
				path: '/price-desk/',
				name: 'pagePriceDesk',
				component: () => import("@/pages/PriceDesk.vue"),
				meta: {
					secure: true
				}
			},
			{
				path: '/profit-loss-calculator/:id',
				name: 'pageProfitLossCalculator',
				component: () => import("@/pages/ProfitLossCalculator.vue"),
				meta: {
					secure: true
				}
			},
			{
				path: '/user-access',
				name: 'pageUserAccess',
				component: () => import("@/views/UserAccessView.vue"),
				meta: {
					secure: true,
				}
			},
			{
				path: '/cmac-extension',
				name: 'pageCmacExtension',
				component: () => import("@/pages/CmacExtensionRequestForm.vue"),
				meta: {
					secure: true
				}
			},
			{
				path: '/vp-rates',
				name: 'vpRateTable',
				component: () => import("@/components/VPRateTable.vue"),
				meta: {
					secure: true
				}
			}
		]
	},
    // Catch all for 404 not found page.
    {
        path: '/:pathMatch(.*)*',
        name: 'fullView',
        component: () => import( "@/views/FullView.vue" ),
        children :
		[
			{
				path: '/:pathMatch(.*)*',
				name: '404',
				component: () => import( "@/pages/404.vue" ),
				meta:
				{
					secure: false
				}
			}
        ]
    }
];

// Create Router Object
const router = createRouter
({
	history : createWebHistory( import.meta.env.BASE_URL ),
	routes
});

// Add event hook to start page loader when page changes.
router.beforeEach ( () =>
{
    const appStore = useAppStore();

    appStore.startPageLoader();
});

/**
 * ------
 * Export
 * ------
 */

export default router;
