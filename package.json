{"name": "channelsupport", "version": "0.0.1", "template-version": "1.0.0", "private": true, "type": "module", "scripts": {"serve-local": "vite --mode localE", "serve-dev": "vite --mode development", "serve-stage": "vite --mode staging", "serve-prod": "vite --mode production", "build-dev": "vite build --mode development", "build-stage": "vite build --mode staging", "build-prod": "vite build --mode production"}, "dependencies": {"@azure/msal-browser": "^3.17.0", "axios": "^1.7.2", "file-saver": "^2.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "pinia": "^2.1.7", "vue": "^3.4.27", "vue-i18n": "^9.13.1", "vue-router": "^4.3.3", "vuetify": "^3.6.8", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.17", "@types/node": "^20.14.2", "@vitejs/plugin-vue": "^5.0.5", "@vue/tsconfig": "^0.5.1", "sass": "^1.77.4", "typescript": "^5.4.5", "vite": "^5.2.13", "vite-plugin-vue-devtools": "^7.2.1", "vite-plugin-vuetify": "^2.0.3", "vue-tsc": "^2.0.21"}}