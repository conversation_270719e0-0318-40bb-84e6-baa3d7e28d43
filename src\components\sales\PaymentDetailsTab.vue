<script setup lang="ts">
/**
 * @file Payment Details tab component for Sales Request Form.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, reactive, watch, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { toDateInputValue } from '@/services/salesRequestService';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';

// Add language support
const { t } = useI18n();

// Form validation state
const formState = reactive({
    errors: {} as Record<string, string>,
    touched: {} as Record<string, boolean>
});

// Form data with validation rules
const paymentDetails = ref({
    pricingLevel: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    minimumCommitment: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    paymentMode: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Payment mode is required'
    },
    paymentJustification: {
        value: '',
        required: false,
        error: '',
        validate: (val: string, form: any) => form.paymentMode.value === 'other' && !val ? 'Payment justification is required when Other is selected' : ''
    },
    leaseTerm: {
        value: '',
        required: false,
        error: '',
        validate: (val: string, form: any) => form.paymentMode.value === 'lease' && !val ? 'Lease term is required' : ''
    },
    typeOfContract: {
        value: '',
        required: false,
        error: '',
        validate: (val: string, form: any) => form.paymentMode.value === 'lease' && !val ? 'Type of contract is required' : ''
    },
    billingPeriod: {
        value: '',
        required: false,
        error: '',
        validate: (val: string, form: any) => form.paymentMode.value === 'lease' && !val ? 'Billing period is required' : ''
    },
    oneMultipleSuppliers: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    implementation: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Implementation is required'
    },
    foreignBuyoutTradeUp: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'This field is required'
    },
    proposalDate: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Proposal date is required'
    }
});
const formattedMinimumCommitment = computed({
  get() {
    const value = paymentDetails.value.minimumCommitment.value;
    if (!value) return '';
    return Number(value).toLocaleString('en-US');
  },
  set(newValue) {
    // Remove all non-digit characters before saving
    const numericValue = newValue.replace(/\D/g, '');
    paymentDetails.value.minimumCommitment.value = numericValue;
  }
});
onMounted(async () => {
  try {
    const pricingLovs = await getLov(LovCategories.PRICING_CATEGORY_CODE);
    pricingLevelOptions.value = pricingLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));

    const paymentLovs = await getLov(LovCategories.PAYMENT_MODE);
    paymentOptions.value = paymentLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));

    const contractLovs = await getLov(LovCategories.CONTRACT_TYPE);
    contractTypeOptions.value = contractLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));

    const leaseLovs = await getLov(LovCategories.LEASE_TERM);
    leaseTermOptions.value = leaseLovs.map((l: LovItem) => ({ title: l.description, value: l.id }));

    const billingLovs = await getLov(LovCategories.BILLING_PERIOD);
    billingOptions.value = billingLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));

    const supplierLovs = await getLov(LovCategories.SUPPLIER_TYPE);
    supplierOptions.value = supplierLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));

    const implLovs = await getLov(LovCategories.IMPLEMENTATION);
    implementationOptions.value = implLovs.map((l: LovItem) => ({ label: l.description, value: l.lookupCode }));
  } catch (e) {
    console.error('Failed to load LOVs', e);
  }
});

// Watch for changes in payment mode to update requirements
watch(() => paymentDetails.value.paymentMode.value, (newMode) => {
    const isLease = newMode === 'LEASE';
    const isOther = newMode === 'OTHER';

    // Update .required flags for label asterisks and conditional logic
    paymentDetails.value.leaseTerm.required = isLease;
    paymentDetails.value.typeOfContract.required = isLease;
    paymentDetails.value.billingPeriod.required = isLease;
    paymentDetails.value.paymentJustification.required = isOther;

    // Clear values of fields that become hidden/irrelevant
    if (!isLease) {
        paymentDetails.value.leaseTerm.value = '';
        paymentDetails.value.typeOfContract.value = '';
        paymentDetails.value.billingPeriod.value = '';
    }
    // Clear payment justification if payment mode is not 'other' and it has a value
    if (!isOther && paymentDetails.value.paymentJustification.value) {
         paymentDetails.value.paymentJustification.value = '';
    }

    // Re-validate touched fields whose requirement/context might have changed.
    // The individual validate() functions are already conditional on paymentMode.
    const fieldsToRevalidate = ['leaseTerm', 'typeOfContract', 'billingPeriod', 'paymentJustification'];
    fieldsToRevalidate.forEach(fieldKey => {
        if (formState.touched[fieldKey]) {
            validateField(fieldKey);
        }
    });
});

// Watch for changes to proposalDate and ensure it's in the correct format
watch(() => paymentDetails.value.proposalDate.value, (newValue) => {
    // If the value is not empty and not in the correct format, format it
    if (newValue && !newValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
        try {
            const date = new Date(newValue);
            if (!isNaN(date.getTime())) {
                const formattedDate = date.toISOString().split('T')[0];
                paymentDetails.value.proposalDate.value = formattedDate;
            }
        } catch (e) {
            console.error('Error formatting date:', e);
        }
    }
});

const pricingLevelOptions = ref<{ title: string; value: string }[]>([]);
// Dynamic LOV-driven dropdown refs for official categories
const paymentOptions = ref<{ title: string; value: string }[]>([]);
const contractTypeOptions = ref<{ title: string; value: string }[]>([]);
const leaseTermOptions = ref<{ title: string | number; value: string | number }[]>([]);
const billingOptions = ref<{ title: string; value: string }[]>([]);
const supplierOptions = ref<{ title: string; value: string }[]>([]);
const implementationOptions = ref<{ label: string; value: string }[]>([]);

// Static dropdowns (not backed by LOV)
const assessmentOptions = ['Yes', 'No'];

// Define minimum commitment values for each pricing level
const pricingLevelMinCommitments = {
  'SBI': '1000000',
  'SPECIAL_SAS': '100000'
};

// Watch for changes in pricingLevel and update minimumCommitment
watch(() => paymentDetails.value.pricingLevel.value, (newLevel) => {
  if (newLevel && pricingLevelMinCommitments[newLevel]) {
    paymentDetails.value.minimumCommitment.value = pricingLevelMinCommitments[newLevel];
  } else {
    paymentDetails.value.minimumCommitment.value = '';
  }
  // Clear any existing error when updating the value
  paymentDetails.value.minimumCommitment.error = '';
});

// Helper function to generate field labels with conditional asterisks
const getFieldLabel = (fieldKey: string) => {
    const field = paymentDetails.value[fieldKey as keyof typeof paymentDetails.value];
    const translationKey = `page.sales_request_form.customer_details.${fieldKey}`;
    return t(translationKey) + (field.required ? ' *' : '');
};

// Validate a single field
const validateField = (fieldName: string) => {
    const field = paymentDetails.value[fieldName as keyof typeof paymentDetails.value];
    field.error = field.validate(field.value || '', paymentDetails.value);
    formState.touched[fieldName] = true;
    return field.error === '';
};

// Validate all fields
const validateForm = () => {
    let isValid = true;
    
    for (const fieldName in paymentDetails.value) {
        if (!validateField(fieldName)) {
            isValid = false;
        }
    }
    
    return isValid;
};

const setFormData = (data: any) => {
    if (!data) return;
    // Map backend fields to frontend state
    const mapping: { [key: string]: string } = {
        pricingCategoryCode: 'pricingLevel',
        minimumCommitmentAmount: 'minimumCommitment',
        foreignBuyout: 'foreignBuyoutTradeUp',
        leaseTermInMonth: 'leaseTerm',
        contractType: 'typeOfContract',
        billingPeriod: 'billingPeriod',
        supplierType: 'oneMultipleSuppliers',
        implementation: 'implementation',
        paymentMode: 'paymentMode',
        paymentJustification: 'paymentJustification'
    };

    const dateFields = ['proposalDate'];

    for (const backendKey in data) {
        const frontendKey = mapping[backendKey] || backendKey;
        if (paymentDetails.value[frontendKey as keyof typeof paymentDetails.value]) {
            let value = data[backendKey];
            if (dateFields.includes(frontendKey) && value) {
                value = toDateInputValue(value);
            }
            paymentDetails.value[frontendKey as keyof typeof paymentDetails.value].value = value;
        }
    }
};

// Expose validation methods and form data to parent component
defineExpose({
    validateForm,
    getFormData: () => {
        const details = paymentDetails.value;
        // Map frontend state to the backend model
        return {
            pricingCategoryCode: details.pricingLevel.value,
            minimumCommitment: details.minimumCommitment.value,
            paymentMode: details.paymentMode.value,
            paymentJustification: details.paymentJustification.value,
            leaseTermInMonth: details.leaseTerm.value,
            typeOfContract: details.typeOfContract.value,
            billingPeriod: details.billingPeriod.value,
            supplierType: details.oneMultipleSuppliers.value,
            implementation: details.implementation.value,
            foreignBuyoutTradeUp: details.foreignBuyoutTradeUp.value,
            proposalDate: details.proposalDate.value,
        };
    },
    setFormData
});
</script>    

<template>
    <v-container fluid>
        <v-row>
            <!-- Left Column -->
            <v-col cols="12" md="6">
                <v-row>
                    <v-col cols="12">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.pricingLevel.value"
                            :items="pricingLevelOptions"
                            :label="getFieldLabel('pricingLevel')"
                            :error-messages="paymentDetails.pricingLevel.error"
                            @blur="validateField('pricingLevel')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field 
                            density="compact"
                            v-model="formattedMinimumCommitment"
                            :label="getFieldLabel('minimumCommitment')"
                            :error-messages="paymentDetails.minimumCommitment.error"
                            prefix="$"
                            readonly
                            variant="outlined"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.paymentMode.value"
                            :items="paymentOptions"
                            item-title="title"
                            item-value="value"
                            :label="getFieldLabel('paymentMode')"
                            :error-messages="paymentDetails.paymentMode.error"
                            @blur="validateField('paymentMode')"
                        ></v-select>
                    </v-col>
                    
                    <!-- Payment Justification (only show if payment mode is other) -->
                    <v-col cols="12" v-if="paymentDetails.paymentMode.value === 'OTHER'">
                        <v-text-field 
                            density="compact"
                            v-model="paymentDetails.paymentJustification.value"
                            :label="getFieldLabel('paymentJustification')"
                            :error-messages="paymentDetails.paymentJustification.error"
                            @blur="validateField('paymentJustification')"
                            :rules="paymentDetails.paymentJustification.required ? [v => !!v || 'Payment justification is required'] : []"
                            placeholder="Please specify payment details"
                        ></v-text-field>
                    </v-col>

                    <v-col cols="12">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.oneMultipleSuppliers.value"
                            :items="supplierOptions"
                            :label="getFieldLabel('oneMultipleSuppliers')"
                            :error-messages="paymentDetails.oneMultipleSuppliers.error"
                            :rules="paymentDetails.oneMultipleSuppliers.required ? [v => !!v || 'This field is required'] : []"
                            @blur="validateField('oneMultipleSuppliers')"
                        ></v-select>
                    </v-col>
                </v-row>
            </v-col>
            
            <!-- Right Column -->
            <v-col cols="12" md="6">
                <v-row>
                    <v-col cols="12">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.implementation.value"
                            :items="implementationOptions"
                            item-title="label"
                            item-value="value"
                            :label="getFieldLabel('implementation')"
                            :error-messages="paymentDetails.implementation.error"
                            :rules="paymentDetails.implementation.required ? [v => !!v || 'Implementation is required'] : []"
                            @blur="validateField('implementation')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field 
                            density="compact"
                            v-model="paymentDetails.foreignBuyoutTradeUp.value"
                            :label="getFieldLabel('foreignBuyoutTradeUp')"
                            :error-messages="paymentDetails.foreignBuyoutTradeUp.error"
                            :rules="paymentDetails.foreignBuyoutTradeUp.required ? [v => !!v || 'This field is required'] : []"
                            @blur="validateField('foreignBuyoutTradeUp')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field 
                            density="compact"
                            v-model="paymentDetails.proposalDate.value"
                            type="date"
                            :label="getFieldLabel('proposalDate')"
                            :error-messages="paymentDetails.proposalDate.error"
                            :rules="paymentDetails.proposalDate.required ? [v => !!v || 'Proposal date is required'] : []"
                            @blur="validateField('proposalDate')"
                        ></v-text-field>
                    </v-col>
                         
                    <!-- Lease Term -->
                    <v-col cols="12" v-if="paymentDetails.paymentMode.value === 'LEASE'">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.leaseTerm.value"
                            :items="leaseTermOptions"
                            item-title="title"
                            item-value="value"
                            :label="getFieldLabel('leaseTerm')"
                            :error-messages="paymentDetails.leaseTerm.error"
                            :rules="paymentDetails.leaseTerm.required ? [v => !!v || 'Lease term is required'] : []"
                            @blur="validateField('leaseTerm')"
                        ></v-select>
                    </v-col>
                    
                    <!-- Type of Contract -->
                    <v-col cols="12" v-if="paymentDetails.paymentMode.value === 'LEASE'">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.typeOfContract.value"
                            :items="contractTypeOptions"
                            item-title="title"
                            item-value="value"
                            :label="getFieldLabel('typeOfContract')"
                            :error-messages="paymentDetails.typeOfContract.error"
                            :rules="paymentDetails.typeOfContract.required ? [v => !!v || 'Type of contract is required'] : []"
                            @blur="validateField('typeOfContract')"
                        ></v-select>
                    </v-col>
                    
                    <!-- Billing Period -->
                    <v-col cols="12" v-if="paymentDetails.paymentMode.value === 'LEASE'">
                        <v-select 
                            density="compact"
                            v-model="paymentDetails.billingPeriod.value"
                            :items="billingOptions"
                            item-title="title"
                            item-value="value"
                            :label="getFieldLabel('billingPeriod')"
                            :error-messages="paymentDetails.billingPeriod.error"
                            :rules="paymentDetails.billingPeriod.required ? [v => !!v || 'Billing period is required'] : []"
                            @blur="validateField('billingPeriod')"
                        ></v-select>
                    </v-col>
                </v-row>
            </v-col>
        </v-row>
    </v-container>
</template>
