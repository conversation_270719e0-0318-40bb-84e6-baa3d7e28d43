<script setup lang="ts">
import { ref, reactive, defineProps, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import ServiceTonerPack from '@/components/service/ServiceTonerPack.vue';
import ServiceApprovalsLease from '@/components/service/ServiceApprovalsLease.vue';
import ServiceProductDetails from '@/components/service/ServiceProductDetails.vue';
import ServiceAccessoryList from '@/components/service/ServiceAccessoryList.vue';
import ServiceCompetitiveInfo from '@/components/service/ServiceCompetitiveInfo.vue';
import ServiceCurrentEquipment from '@/components/service/ServiceCurrentEquipment.vue';
import ServiceBusinessCase from '@/components/service/ServiceBusinessCase.vue';
import * as XLSX from 'xlsx';
import { getHardwareChargesRates } from '@/lib/api';
import { saveServiceForm , getServiceForm } from '@/services/salesRequestService';

const { t } = useI18n();

const props = defineProps({
  // Summary of MSRP from WorksheetsTab { amount:number, percentage:number }
  msrpSummary: {
    type: Object as () => { amount: number; percentage: number },
    required: false,
    default: () => ({ amount: 0, percentage: 0 }),
  },
  // Payment information from PaymentDetailsTab (whatever getFormData returns)
  paymentInfo: {
    type: Object as () => Record<string, any>,
    required: false,
    default: () => ({}),
  },
  // List of hardware main unit itemIds coming from WorksheetsTab
  hardwareMainUnitIds: {
    type: Array as () => number[],
    required: false,
    default: () => [],
  },
  // Selected portfolioId from CustomerDetailsTab
  portfolioId: {
    type: Number,
    required: false,
    default: null,
  },
  // The current Sales Request ID to associate with this Service Form
  requestId: {
    type: Number,
    required: false,
    default: null,
  },
  // Existing Service Form ID (for edit mode)
  serviceFormId: {
    type: Number,
    required: false,
    default: null,
  },
  // Map of itemId -> { dsd: number|null, dealer: number|null }
  itemQuantities: {
    type: Object as () => Record<number, { dsd: number | null; dealer: number | null }>,
    required: false,
    default: () => ({}),
  },
});

// Initialize formData (copied from original page)
const formData = reactive({
  service_request_form: {
    request_date: null,
    sales_representative_name: '',
    sales_manager_name: '',
    customer_business_name: '',
    customer_legal_name: '',
    address1: '',
    address2_3: '',
    city_province_postal: '',
  },
  toner_and_service_value_pack: {
    toner_in_out: null,
    service_value_pack: '',
  },
  approvals_details: {
    selected_region: null,
    dsd_territory: false,
    dealer_territory: false,
    dealer_accepted_service_rates: null,
    purchase_lease_type: 'Lease',
    length_of_lease_months: null,
    msrp_details: { percentage: null, amount: null },
  },
  product_service_details: {
    models_overview: [],
    accessories_included: [],
  },
  competitive_current_usage_info: {
    details: '',
  },
  current_equipment_details: {
    equipment_list: '',
  },
  service_business_case: {
    justification: '',
  },
});

// ---------------- Hardware Charges Fetch ----------------
const hardwareCharges = ref<Record<number, any>>({});

const fetchHardwareCharges = async () => {
  const servicePackIdRaw = formData.toner_and_service_value_pack.service_value_pack;
  const servicePackId = servicePackIdRaw ? Number(servicePackIdRaw) : null;
  const portfolioId = props.portfolioId ? Number(props.portfolioId) : null;
  if (!servicePackId || !portfolioId || !props.hardwareMainUnitIds || props.hardwareMainUnitIds.length === 0) return;

  const chargesMap: Record<number, any> = {};
  await Promise.all(
    props.hardwareMainUnitIds.map(async (itemId) => {
      try {
        const { data } = await getHardwareChargesRates(itemId, servicePackId, portfolioId);
        chargesMap[itemId] = data;
      } catch (e) {
        console.error('Failed to fetch hardware charges for', itemId, e);
      }
    })
  );
  hardwareCharges.value = chargesMap;
  console.log('Hardware charges fetched:', hardwareCharges.value);

  // Map fetched charges into models_overview structure expected by ServiceProductDetails
  const qtyMap = props.itemQuantities || {};
  const existingModels: any[] = formData.product_service_details.models_overview || [];

  const mappedProducts = Object.values(chargesMap).map((c: any) => {
    // If we already loaded an existing service form, preserve its extra fields
    const existing = existingModels.find((m) => m.id === c.itemId) || {};

    return {
      // Hardware-rate fields (always replace with latest)
      ...existing,
      id: c.itemId,
      model: c.displayName,
      blackAndWhite: c.blackAndWhite,
      colorValue: c.colorValue,
      iprc: c.oversizedValue,
      minimumBase: c.minimumBase,
      minimumVolume: c.minimumVolume,

      // Quantities (prefer latest from qtyMap, fallback to existing)
      dsd_qty: qtyMap[c.itemId]?.dsd ?? existing.dsd_qty ?? null,
      dealer_qty: qtyMap[c.itemId]?.dealer ?? existing.dealer_qty ?? null,

      // The following fields are preserved if they already exist, otherwise default
      estimated_amv_unit: existing.estimated_amv_unit ?? null,
      colour_percentage: existing.colour_percentage ?? null,
      oversize_percentage: existing.oversize_percentage ?? null,
      fees_included_in_cpc: existing.fees_included_in_cpc ?? false,
      discounts: existing.discounts || { bw: null, colour: null, minimum_base_amt: null, minimum_base_volume: null },
      serviceOptions: existing.serviceOptions || [],
    };
  });

  formData.product_service_details.models_overview = mappedProducts;
};

watch(
  () => [
    (props.hardwareMainUnitIds ?? []).slice(),
    props.portfolioId,
    formData.toner_and_service_value_pack.service_value_pack,
    props.itemQuantities,
  ],
  fetchHardwareCharges,
  { deep: true }
);

// Watch for serviceFormId changes
watch(() => props.serviceFormId, loadExistingServiceForm);

const init = async () => {
  await fetchHardwareCharges();
  await loadExistingServiceForm();
};

onMounted(init);

async function loadExistingServiceForm() {
  if (!props.serviceFormId) return;
  try {
    const { data } = await getServiceForm(props.serviceFormId);
    // Map top-level fields
    formData.toner_and_service_value_pack.service_value_pack = String(data.servicePackId) ?? null;
    formData.toner_and_service_value_pack.toner_in_out = data.tonerType ?? null;
    formData.approvals_details.selected_region = data.region ?? null;
    formData.approvals_details.territory = data.territory ?? null;
    formData.approvals_details.dealer_accepted_service_rates = data.isDealerAcceptedSr ?? null;
    formData.approvals_details.purchase_lease_type = data.paymentModeValue ?? null;
    formData.approvals_details.length_of_lease_months = data.leaseTermInMonth ?? null;
    formData.approvals_details.msrp_details = { amount: data.msrp, percentage: data.msrpPercent };
    formData.competitive_current_usage_info.details = data.currentUsageInformation ?? '';
    formData.current_equipment_details.equipment_list = data.currentEquipmentInfo ?? '';
    formData.service_business_case.justification = data.serviceBusinessCase ?? '';

    // Build models_overview from serviceApprovals
    const grouped: Record<number, any> = {};
    (data.serviceApprovals || []).forEach((sa: any) => {
      if (!grouped[sa.itemId]) {
        grouped[sa.itemId] = {
          id: sa.itemId,
          model: sa.displayName,
          blackAndWhite: sa.blackAndWhite ?? null,
          colorValue: sa.colour ?? null,
          iprc: sa.iprc ?? null,
          minimumBase: sa.minimumBaseAmount ?? null,
          minimumVolume: sa.minimumBaseVolume ?? null,
          dsd_qty: sa.dsdQuantity ?? null,
          dealer_qty: sa.dealerQuantity ?? null,
          estimated_amv_unit: sa.amvUnit ?? null,
          colour_percentage: sa.colourPercentage ?? null,
          oversize_percentage: sa.oversizePercentage ?? null,
          serviceApprovalId: sa.serviceApprovalId ?? null,
          fees_included_in_cpc: sa.cpcIncludesAccessory === 'Y',
          discounts: { bw: null, colour: null, iprc: null, minimum_base_amt: null, minimum_base_volume: null },
          serviceOptions: [],
        };
      }
      grouped[sa.itemId].serviceOptions.push({
        id: `opt_${Math.random()}`,
        serviceApprovalId: sa.serviceApprovalId ?? null,
        fixed_service_term_length: sa.fixedTermInMonth ?? null,
        bw: sa.blackAndWhite ?? null,
        colour: sa.colour ?? null,
        iprc: sa.iprc ?? null,
        minimum_base_amt: sa.minimumBaseAmount ?? null,
        minimum_base_volume: sa.minimumBaseVolume ?? null,
      });
    });
    formData.product_service_details.models_overview = Object.values(grouped);
  } catch (e) {
    console.error('Failed to load service form', e);
  }
}

const handleFormSubmit = async () => {
  // Construct payload according to required backend structure
  const payload: Record<string, any> = {
    serviceFormId: props.serviceFormId ?? null,
    requestId: props.requestId ?? null, // Expecting parent to pass requestId prop
    servicePackId: formData.toner_and_service_value_pack.service_value_pack
      ? Number(formData.toner_and_service_value_pack.service_value_pack)
      : null,
    currentUsageInformation: formData.competitive_current_usage_info.details || null,
    currentEquipmentInfo: formData.current_equipment_details.equipment_list || null,
    serviceBusinessCase: formData.service_business_case.justification || null,
    tonerType: formData.toner_and_service_value_pack.toner_in_out || null,
    region: formData.approvals_details.selected_region || null,
    territory: formData.approvals_details.territory || null,
    isDealerAcceptedSr: formData.approvals_details.dealer_accepted_service_rates || null,

    // Build service approvals by iterating over each model and its service options
    serviceApprovals: [] as any[],

    // Build MFP pricing list
    dsdRequestMfpPricing: [] as any[],
  };

  // ---- Service Approvals ----
  (formData.product_service_details.models_overview || []).forEach((model: any) => {
    const baseApprovalData = {
      serviceApprovalId: model.serviceApprovalId ?? null,
      serviceFormId: props.serviceFormId ?? null,
      itemId: model.id,
      dsdQuantity: model.dsd_qty ?? null,
      dealerQuantity: model.dealer_qty ?? null,
      minimumBaseAmount: model.minimumBase ?? null,
      minimumBaseVolume: model.minimumVolume ?? null,
      userRemark: 'Static/Placeholder Message For now',
      amvUnit: model.estimated_amv_unit ?? null,
      colourPercentage: model.colour_percentage ?? null,
      oversizePercentage: model.oversize_percentage ?? null,
      cpcIncludesAccessory: model.fees_included_in_cpc ? 'Y' : 'N',
    };

    // If serviceOptions exist, create approval rows per option, otherwise create one generic row.
    if (Array.isArray(model.serviceOptions) && model.serviceOptions.length) {
      model.serviceOptions.forEach((opt: any) => {
        payload.serviceApprovals.push({
          ...baseApprovalData,
          serviceApprovalId: opt.serviceApprovalId ?? null,
          fixedTermInMonth: opt.fixed_service_term_length ?? null,
          blackAndWhite: opt.bw ?? null,
          colour: opt.colour ?? null,
          iprc: opt.iprc ?? null,
          minimumBaseAmount: opt.minimum_base_amt ?? model.minimumBase ?? null,
          minimumBaseVolume: opt.minimum_base_volume ?? model.minimumVolume ?? null,
        });
      });
    } else {
      // Fallback single row
      payload.serviceApprovals.push({
        ...baseApprovalData,
        serviceApprovalId: model.serviceApprovalId ?? null,
        fixedTermInMonth: null,
        blackAndWhite: model.blackAndWhite ?? null,
        colour: model.colorValue ?? null,
        iprc: null,
      });
    }
  });

  // ---- MFP Pricing ----
  if ((formData.product_service_details.accessories_included || []).length > 0) {
    (formData.product_service_details.models_overview || []).forEach((model: any) => {
      // Find first service option requested rate as requestedRate example
      let requestedRate = null;
      let publishedRate = null;
      if (Array.isArray(model.serviceOptions) && model.serviceOptions.length) {
        const opt = model.serviceOptions[0];
        requestedRate = opt.colour ?? null;
        publishedRate = model.colorValue ?? null;
      }

      payload.dsdRequestMfpPricing.push({
        mfpPricingId: null,
        mfpModel: model.model,
        accessoryIncluded: model.fees_included_in_cpc ? 'Accessory Included in Deal' : 'No Accessory',
        publishedRate,
        requestedRate,
      });
    });
  }
  // else keep default empty array

  console.log('Constructed Service Form payload:', payload);
  try {
    const { data } = await saveServiceForm(payload);
    console.log('Service Form saved successfully:', data);
    // TODO: Emit event or show notification as needed
  } catch (error) {
    console.error('Failed to save Service Form:', error);
    // TODO: Show error notification to user
  }
};

const cancelForm = () => {
  console.log('Service Form cancelled');
};

const exportData = () => {
  const wb = XLSX.utils.book_new();
  // Only very basic export for now
  const summaryWs = XLSX.utils.json_to_sheet([{ form: 'service', data: formData }]);
  XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');
  XLSX.writeFile(wb, 'Service-Request-Export.xlsx');
};



// ---- Sync external summaries into local form data ----
const syncMsrp = () => {
  formData.approvals_details.msrp_details.amount = props.msrpSummary.amount ?? 0;
  formData.approvals_details.msrp_details.percentage = props.msrpSummary.percentage ?? 0;
};
const syncPayment = () => {
  formData.approvals_details.purchase_lease_type = props.paymentInfo.paymentMode ?? '';
  formData.approvals_details.length_of_lease_months = props.paymentInfo.leaseTermInMonth ?? null;
};

// Initial sync
syncMsrp();
syncPayment();

// Watch for changes
watch(() => props.msrpSummary, syncMsrp, { deep: true });
watch(() => props.paymentInfo, syncPayment, { deep: true });

onMounted(() => {
  if (props.id) {
    console.log('ServiceFormTab mounted with ID:', props.id);
  }
});

// Expose methods for parent component
defineExpose({
  getFormData: () => {
    return formData;
  },
  submitServiceForm: handleFormSubmit
});
</script>

<template>
  <v-container fluid>
    <v-form @submit.prevent="handleFormSubmit">
      <v-row>
        <v-col cols="12">
          <h1>{{ t('page.service_request_form.title', 'Service Form') }}</h1>
        </v-col>
      </v-row>

      <!-- Toner and Service Value Pack -->
      <ServiceTonerPack v-model="formData.toner_and_service_value_pack" />

      <!-- Approvals and Lease Details -->
      <ServiceApprovalsLease v-model="formData.approvals_details" />

      <!-- Product/Service Details -->
      <ServiceProductDetails 
        v-model="formData.product_service_details.models_overview"
        :selected-service-value-pack="formData.toner_and_service_value_pack.service_value_pack"
      />

      <!-- Accessories Included -->
      <ServiceAccessoryList v-model="formData.product_service_details.accessories_included" />

      <!-- Competitive & Current Usage Information -->
      <ServiceCompetitiveInfo v-model="formData.competitive_current_usage_info.details" />

      <!-- Current Equipment Details -->
      <ServiceCurrentEquipment v-model="formData.current_equipment_details.equipment_list" />

      <!-- Service Business Case -->
      <ServiceBusinessCase v-model="formData.service_business_case.justification" />

      <v-row>
        <v-col cols="12" class="d-flex justify-end mt-4">
          <v-btn color="secondary" prepend-icon="exit_to_app" variant="outlined" @click="exportData" class="mr-2">
            Export
          </v-btn>
          <!-- <v-btn color="primary" type="submit">
            Submit
          </v-btn> -->
        </v-col>
      </v-row>
    </v-form>
  </v-container>
</template>

<style scoped>
</style>
