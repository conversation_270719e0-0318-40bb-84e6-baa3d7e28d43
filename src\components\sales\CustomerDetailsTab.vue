<script setup lang="ts">
/**
 * @file Customer Details tab component for Sales Request Form.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue';

// Define interfaces for Customer and Location based on API response
interface Location {
    locationId: number;
    customerId: number;
    displayName: string | null;
    locationType: string;
    isPrimary: 'Y' | 'N';
    addressLine1: string;
    addressLine2: string | null;
    addressLine3: string | null;
    city: string;
    state: string;
    country: string;
    postalCode: string;
}

interface Customer {
    customerId: number;
    businessName: string;
    displayName: string;
    legalName: string;
    customerCode: string;
    status: string;
    sfOpportunityId: string;
    relationshipStatus: string;
    customerType: string;
    website: string;
    region: string;
    isGlobalAgreement: 'Y' | 'N';
    isSubsidiary: 'Y' | 'N';
    locations: Location[];
}
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/stores/UserStore';
import LocationManager from '@/components/sales/LocationManager.vue';
import CustomerManager from '@/components/sales/CustomerManager.vue';
import { toDateInputValue, getCustomers } from '@/services/salesRequestService';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';

// Define props
const props = defineProps<{
  validationErrors?: Record<string, string>;
}>();

// Helper function to get validation error for a field
const getValidationError = (fieldKey: string): string => {
  return props.validationErrors?.[fieldKey] || '';
};

// Helper function to check if field has validation error
const hasValidationError = (fieldKey: string): boolean => {
  return Boolean(props.validationErrors?.[fieldKey]);
};

// Add language support
const { t } = useI18n();

// Get user store for logged-in user info
const userStore = useUserStore();

// Add a ref for the date picker menu
const rfpDueDateMenu = ref(false);

// State for the customer list
const allCustomers = ref<Customer[]>([]);
const customersLoading = ref(false);
const customerLoadError = ref('');
const managerList = ref<{ key: any; value: any }[]>([]);

// Fetch customers and LOVs when the component is mounted
onMounted(async () => {
    customersLoading.value = true;
    // Fetch LOVs concurrently
    const lovPromises = [
        getLov(LovCategories.SALES_CHANNEL),
        getLov(LovCategories.PORTFOLIO),
        getLov(LovCategories.BRANCH)
    ];
      managerList.value=[{key:userStore.manager?.microsoftID,value:userStore.manager?.displayName}]
    customerLoadError.value = '';
    try {
        allCustomers.value = await getCustomers();
        const [channelLovs, portfolioLovs, branchLovs] = await Promise.all(lovPromises);
        channelOptions.value = channelLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        portfolioOptions.value = portfolioLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        branchOptions.value = branchLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
    } catch (err: any) {
        customerLoadError.value = err?.message || 'Failed to load customers';
    } finally {
        customersLoading.value = false;
    }
});

const customerLocations = ref<Location[]>([]);

// Dropdown option refs for LOV-driven fields
const channelOptions = ref<{ title: string; value: string }[]>([]);
const portfolioOptions = ref<{ title: string; value: string }[]>([]);
const branchOptions = ref<{ title: string; value: string }[]>([]);

// Compute sales rep options based on logged-in user
const salesRepOptions = computed(() => {
    if (userStore.authenticated) {
        return [{
            title: `${userStore.givenName} ${userStore.surName}`,
            value: userStore.microsoftID
        }];
    }
    return [];
});

// Form validation state
const formState = reactive({
    errors: {} as Record<string, string>,
    touched: {} as Record<string, boolean>
});

// Form data with validation rules
const customerDetails = ref({
    businessName: {
        value: null as Customer | null,
        required: true,
        error: '',
        validate: (val: Customer | null) => val ? '' : 'Business name is required'
    },
    legalName: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    salesforceOppId: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    customerRelationship: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Customer relationship is required'
    },
    rfp: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'RFP selection is required'
    },
    rfpDueDate: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'RFP due date is required'
    },
    location: {
        value: null as Location | null,
        required: true,
        error: '',
        validate: (val: Location | null) => val ? '' : 'Location is required'
    },
    customerWebsite: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => {
            if (!val) return 'Customer website is required';
            // Basic URL validation
            try {
                new URL(val.startsWith('http') ? val : `https://${val}`);
                return '';
            } catch {
                return 'Invalid website URL';
            }
        }
    },
    globalAgreement: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Global agreement is required'
    },
    branch: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    salesRep: {
        value: userStore.authenticated ? userStore.microsoftID : '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Sales rep is required'
    },
    salesManager: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    regionalLeader: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    salesChannel: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Sales channel is required'
    },
    portfolio: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    printAssessment: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Print assessment is required'
    },
    coreItsNetworkScan: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'CORE ITS network scan is required'
    },
    msa: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'MSA is required'
    }
});

// Watch for changes in the selected customer to autofill other fields
watch(() => customerDetails.value.businessName.value, (newCustomer) => {
    if (newCustomer) {
        customerDetails.value.legalName.value = newCustomer.legalName;
        customerDetails.value.customerWebsite.value = newCustomer.website;
        customerDetails.value.globalAgreement.value = newCustomer.isGlobalAgreement;
        customerDetails.value.salesforceOppId.value = newCustomer.sfOpportunityId;
        customerDetails.value.customerRelationship.value = newCustomer.relationshipStatus;
        customerLocations.value = newCustomer.locations;
        // Reset location selection
        customerDetails.value.location.value = null;
    } else {
        // Clear fields if customer is deselected
        customerDetails.value.legalName.value = '';
        customerDetails.value.customerWebsite.value = '';
        customerDetails.value.globalAgreement.value = '';
        customerDetails.value.salesforceOppId.value = '';
        customerDetails.value.customerRelationship.value = '';
        customerLocations.value = [];
        customerDetails.value.location.value = null;
    }
}, { deep: true });

// Helper function to generate field labels with conditional asterisks
const getFieldLabel = (fieldKey: string) => {
    const field = customerDetails.value[fieldKey as keyof typeof customerDetails.value];
    const translationKey = `page.sales_request_form.customer_details.${fieldKey}`;
    return t(translationKey) + (field.required ? ' *' : '');
};

// Validate a single field
const validateField = (fieldName: string) => {
    const field = customerDetails.value[fieldName as keyof typeof customerDetails.value];
    field.error = field.validate(field.value || '');
    formState.touched[fieldName] = true;
    return field.error === '';
};

// Validate all fields
const validateForm = () => {
    let isValid = true;
    
    for (const fieldName in customerDetails.value) {
        if (!validateField(fieldName)) {
            isValid = false;
        }
    }
    
    return isValid;
};



const setFormData = (data: any) => {
    if (!data) return;

    // If we have a businessId, find the full customer object from the fetched list.
    const customerIdToUse = data.businessId ?? data.customerId;
    if (customerIdToUse) {
        const customer = allCustomers.value.find(c => c.customerId === customerIdToUse);
        if (customer) {
            customerDetails.value.businessName.value = customer;
            // Once customer is set, its locations are available. Now find the location.
            // Wait for the next DOM update cycle to ensure the locations list is populated
            nextTick(() => {
                if (data.locationId && customer.locations) {
                    const location = customer.locations.find(l => l.locationId === data.locationId);
                    if (location) {
                        customerDetails.value.location.value = location;
                    } else {
                        console.error(`Failed to find location with ID: ${data.locationId} for customer ${data.businessId}`);
                    }
                }
            });
        } else {
            console.error(`Failed to find customer with ID: ${data.businessId}`);
        }
    }

    // Map other backend fields to frontend state
    const mapping: { [key: string]: string } = {
        salesRepOid: 'salesRep',
        salesManagerOid: 'salesManager',
        regionalLeaderOid: 'regionalLeader',
        networkScan: 'coreItsNetworkScan',
        customerRelationship: 'customerRelationship',
        isGlobalAgreement: 'globalAgreement',
        isRfp: 'rfp', 
        isMsa: 'msa',
        printAssessment: 'printAssessment',
        salesBranch:'branch'
    };

    const dateFields = ['rfpDueDate'];

    for (const backendKey in data) {
        // Skip fields handled above
        if (backendKey === 'businessId' || backendKey === 'locationId') continue;

        const frontendKey = mapping[backendKey] || backendKey;
        if (customerDetails.value.hasOwnProperty(frontendKey)) {
            let value = data[backendKey];

            // If the value for the portfolio is a nested object, extract the ID
            if (frontendKey === 'portfolio' && typeof value === 'object' && value !== null) {
                value = String(value.portfolioId);
            }
            if (dateFields.includes(frontendKey) && value) {
                value = toDateInputValue(value);
            }
            (customerDetails.value as any)[frontendKey].value = value;
        }
    }
};

// Expose validation methods and form data to parent component
defineExpose({
    validateForm,
    getFormData: () => {
        const details = customerDetails.value;
        return {
            businessId: details.businessName.value?.customerId,
            legalName: details.legalName.value,
            salesforceOppId: details.salesforceOppId.value,
            customerRelationship: details.customerRelationship.value,
            rfp: details.rfp.value,
            rfpDueDate: details.rfpDueDate.value,
            locationId: details.location.value?.locationId, // Send only the ID
            customerWebsite: details.customerWebsite.value,
            globalAgreement: details.globalAgreement.value,
            branch: details.branch.value,
            salesRepOid: details.salesRep.value,
            salesManagerOid: details.salesManager.value,
            regionalLeaderOid: details.regionalLeader.value,
            salesChannel: details.salesChannel.value,
            portfolioId: details.portfolio.value,
            printAssessment: details.printAssessment.value,
            coreItsNetworkScan: details.coreItsNetworkScan.value,
            msa: details.msa.value,
        };
    },
    setFormData
});

// Sample dropdown options
const customerRelationshipOptions = ['Good', 'Strong', 'Fair','Weak'];
const rfpOptions = [
  { label: 'Yes', value: 'Y' },
  { label: 'No', value: 'N' }
];
const agreementOptions = [
  { label: 'Yes', value: 'Y' },
  { label: 'No', value: 'N' }
];

const assessmentOptions = [
  { label: 'Yes', value: 'Y' },
  { label: 'No', value: 'N' }
];
</script>

<template>
    <v-container fluid>
        <v-row>
            <!-- Left Column -->
            <v-col cols="12" md="6">
                <v-row>
                    <v-col cols="12">
                        <CustomerManager
                            v-model="customerDetails.businessName.value"
                            :error="customerDetails.businessName.error"
                            :customers="allCustomers"
                            :loading="customersLoading"
                            @validate="validateField('businessName')"
                        />
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field density="compact"
                            v-model="customerDetails.legalName.value"
                            :label="getFieldLabel('legalName')"
                            :error-messages="customerDetails.legalName.error"
                            @blur="validateField('legalName')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field density="compact"
                            v-model="customerDetails.salesforceOppId.value"
                            :label="getFieldLabel('salesforceOppId')"
                            :error-messages="customerDetails.salesforceOppId.error"
                            @blur="validateField('salesforceOppId')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.customerRelationship.value"
                            :items="customerRelationshipOptions"
                            :label="getFieldLabel('customerRelationship')"
                            :error-messages="customerDetails.customerRelationship.error || getValidationError('customerRelationship')"
                            :error="hasValidationError('customerRelationship') || Boolean(customerDetails.customerRelationship.error)"
                            @blur="validateField('customerRelationship')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
    v-model="customerDetails.rfp.value"
    :items="rfpOptions"
    item-title="label"
    item-value="value"
    :label="getFieldLabel('rfp')"
    :error-messages="customerDetails.rfp.error"
    @blur="validateField('rfp')"
></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-menu
                            v-model="rfpDueDateMenu"
                            :close-on-content-click="false"
                            transition="scale-transition"
                            offset-y
                            min-width="auto"
                        >
                            <template v-slot:activator="{ props }">
                                <v-text-field
                                    density="compact"
                                    v-model="customerDetails.rfpDueDate.value"
                                    :label="getFieldLabel('rfpDueDate')"
                                    :error-messages="customerDetails.rfpDueDate.error"
                                    type="date"
                                    v-bind="props"
                                    @blur="validateField('rfpDueDate')"
                                ></v-text-field>
                            </template>
                        </v-menu>
                    </v-col>
                    
            
                </v-row>
            </v-col>
            
            <!-- Right Column -->
            <v-col cols="12" md="6">
                <v-row>
                    <v-col cols="12">
                        <LocationManager
                            v-model="customerDetails.location.value"
                            :locations="customerLocations"
                            :error="customerDetails.location.error"
                            :customer-id="customerDetails.businessName.value?.customerId"
                            @validate="validateField('location')"
                        />
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field density="compact"
                            v-model="customerDetails.customerWebsite.value"
                            :label="getFieldLabel('customerWebsite')"
                            :error-messages="customerDetails.customerWebsite.error"
                            @blur="validateField('customerWebsite')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
    v-model="customerDetails.globalAgreement.value"
    :items="agreementOptions"
    item-title="label"
    item-value="value"
    :label="getFieldLabel('globalAgreement')"
    :error-messages="customerDetails.globalAgreement.error || getValidationError('isGlobalAgreement')"
    :error="hasValidationError('isGlobalAgreement') || Boolean(customerDetails.globalAgreement.error)"
    @blur="validateField('globalAgreement')"
></v-select>
                    </v-col>
                    
                 
                    
                    <v-col cols="12">
                        <v-select density="compact"
    v-model="customerDetails.coreItsNetworkScan.value"
    :items="assessmentOptions"
    item-title="label"
    item-value="value"
    :label="getFieldLabel('coreItsNetworkScan')"
    :error-messages="customerDetails.coreItsNetworkScan.error"
    @blur="validateField('coreItsNetworkScan')"
></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
    v-model="customerDetails.msa.value"
    :items="assessmentOptions"
    item-title="label"
    item-value="value"
    :label="getFieldLabel('msa')"
    :error-messages="customerDetails.msa.error"
    @blur="validateField('msa')"
></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
    v-model="customerDetails.printAssessment.value"
    :items="assessmentOptions"
    item-title="label"
    item-value="value"
    :label="getFieldLabel('printAssessment')"
    :error-messages="customerDetails.printAssessment.error"
    @blur="validateField('printAssessment')"
></v-select>
                    </v-col>
                </v-row>
            </v-col>
        </v-row>
        
        <!-- Sales Rep Info Section -->
        <v-row class="mt-4">
            <v-col cols="12">
                <v-card>
                    <v-card-title class="text-h6 bg-grey-lighten-3">
                        Sales Rep Info
                    </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" md="4">
                                <v-select density="compact"
                                    v-model="customerDetails.salesRep.value"
                                    :items="salesRepOptions"
                                    item-title="title"
                                    item-value="value"
                                    :label="getFieldLabel('salesRep')"
                                    readonly
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12" md="4">
                                <v-select density="compact"
                                    v-model="customerDetails.salesManager.value"
                                    :items="managerList"
                                    item-title="value"
                                    item-value="key"
                                    :error-messages="customerDetails.salesManager.error"
                                    @blur="validateField('salesManager')"
                                    :label="getFieldLabel('salesManager')"
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12" md="4">
                                <v-select density="compact"
                                    v-model="customerDetails.regionalLeader.value"
                                    :items="salesRepOptions"
                                    item-title="title"
                                    item-value="value"
                                    :error-messages="customerDetails.regionalLeader.error"
                                    @blur="validateField('regionalLeader')"
                                    :label="getFieldLabel('regionalLeader')"
                                    readonly
                                ></v-select>
                            </v-col>
                                 <v-col cols="12"  md="4">
                        <v-select density="compact"
                            v-model="customerDetails.salesChannel.value"
                            :items="channelOptions"
                            item-title="title"
                            item-value="value"
                            :label="getFieldLabel('salesChannel')"
                            :error-messages="customerDetails.salesChannel.error"
                            @blur="validateField('salesChannel')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12"  md="4">
                        <v-select density="compact"
                            v-model="customerDetails.portfolio.value"
                            :items="portfolioOptions"
                            item-title="title"
                            item-value="value"
                            :label="getFieldLabel('portfolio')"
                            :error-messages="customerDetails.portfolio.error"
                            @blur="validateField('portfolio')"
                        ></v-select>
                    </v-col>
                    <v-col cols="12" md="4">
                        <v-select density="compact"
                             v-model="customerDetails.branch.value"
                             :items="branchOptions"
                             :label="getFieldLabel('branch')"
                             :error-messages="customerDetails.branch.error"
                             item-title="title"
                             item-value="value"
                             @blur="validateField('branch')"
                         ></v-select>
                    </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
























