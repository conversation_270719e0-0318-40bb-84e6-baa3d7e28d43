<script setup lang="ts">
/**
 * @file Price Desk page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import DataTable from '@/components/common/DataTable.vue';
import { useAppStore } from '@/stores/AppStore';
import { useRouter } from 'vue-router';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();
const router = useRouter();

// Table data
const loading = ref(false);
const headers = ref([
    { title: t('page.price_desk.table.header.id'), key: 'id' },
    { title: t('page.price_desk.table.header.product'), key: 'product' },
    { title: t('page.price_desk.table.header.list_price'), key: 'listPrice' },
    { title: t('page.price_desk.table.header.discount'), key: 'discount' },
    { title: t('page.price_desk.table.header.final_price'), key: 'finalPrice' },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref([
    { id: 'PD-001', product: 'Canon EOS R5', listPrice: '$3,899.00', discount: '10%', finalPrice: '$3,509.10' },
    { id: 'PD-002', product: 'Canon EOS R6', listPrice: '$2,499.00', discount: '5%', finalPrice: '$2,374.05' },
    { id: 'PD-003', product: 'Canon RF 70-200mm', listPrice: '$2,699.00', discount: '15%', finalPrice: '$2,294.15' },
    { id: 'PD-004', product: 'Canon EOS R3', listPrice: '$5,999.00', discount: '8%', finalPrice: '$5,519.08' },
]);

// Handle table actions
const handleAction = ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
    router.push({ name: 'pageProfitLossCalculator', params: { id: item.requestId } });

        // Handle edit action
        console.log('Edit price:', item);
    } else if (action === 'approve') {
        // Handle approve action
        console.log('Approve price:', item);
    }
};

// Load data
onMounted(() => {
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
    <div class="pa-4">
        <h1>{{ t('page.price_desk.title') }}</h1>
        
        <!-- <v-card class="mt-4"> -->
        
            
            <v-card-text>
                <DataTable 
                    :headers="headers" 
                    :items="items" 
                    :loading="loading"
                    @action="handleAction"
                >
                    <template v-slot:item.discount="{ item }">
                        <span :class="parseFloat(item.discount) > 10 ? 'text-error' : ''">
                            {{ item.discount }}
                        </span>
                    </template>
               <template v-slot:item.actions="{ item }">
                        <v-menu>
                            <template v-slot:activator="{ props }">
                                <v-btn
                                    icon="more_vert"
                                    variant="text"
                                    size="small"
                                    v-bind="props"
                                ></v-btn>
                            </template>
                            <v-list>
                                <v-list-item
                                    prepend-icon="visibility"
                                    title="View P&L Form"
                                    @click="handleAction({ action: 'view', item })"
                                ></v-list-item>
                                <v-list-item
                                    
                                    prepend-icon="edit"
                                    title="Edit"
                                    @click="handleAction({ action: 'edit', item })"
                                ></v-list-item>
                             
                           
                            </v-list>
                        </v-menu>
                    </template>
                </DataTable>
            </v-card-text>
        <!-- </v-card> -->
    </div>
</template>
