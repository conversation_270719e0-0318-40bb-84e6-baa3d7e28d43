<script setup lang="ts">
/**
 * @file Customer Manager component for managing customer information.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, reactive, computed, watch } from 'vue';
import { addCustomer as addCustomerApi, AddCustomerPayload, Customer as ApiCustomer } from '@/services/salesRequestService';
import { useI18n } from 'vue-i18n';

// Add language support
const { t } = useI18n();

// Define the Location and Customer interfaces based on the API response
interface Location {
    locationId: number;
    customerId: number;
    displayName: string | null;
    locationType: string;
    isPrimary: 'Y' | 'N';
    addressLine1: string;
    addressLine2: string | null;
    addressLine3: string | null;
    city: string;
    state: string;
    country: string;
    postalCode: string;
}

interface Customer {
    customerId: number;
    businessName: string;
    displayName: string;
    legalName: string;
    customerCode: string;
    status: string;
    sfOpportunityId: string;
    relationshipStatus: string;
    customerType: string;
    website: string;
    region: string;
    isGlobalAgreement: 'Y' | 'N';
    isSubsidiary: 'Y' | 'N';
    locations: Location[];
}

// Props
const props = defineProps<{
    modelValue: Customer | null;
    error: string;
    customers: Customer[];
    loading: boolean;
}>();

// Emits
const emit = defineEmits(['update:modelValue', 'validate']);



// Search term for autocomplete
const searchTerm = ref('');

// Dialog state
const showDialog = ref(false);

// New customer form
const newCustomer = reactive<Omit<Customer, 'id'>>({
    businessName: '',
    legalName: '',
    salesforceOppId: '',
    customerRelationship: '',
    customerWebsite: '',
    region: '',
    isGlobalAgreement: 'N',
    isSubsidiary: 'N'
});

// Form validation state
const formErrors = reactive({
    businessName: '',
    customerRelationship: '',
    customerWebsite: '',
    region: ''
});

// Customer relationship options
const customerRelationshipOptionss = ['Good', 'Strong', 'Fair', 'Weak'];

// Region dropdown options
const regionOptions = [
    { value: 'REGION_CENTRAL', title: 'Central' },
    { value: 'REGION_EASTERN', title: 'Eastern' },
    { value: 'REGION_WESTERN', title: 'Western' }
];

// Validate the form
const validateForm = () => {
    let isValid = true;
    
    // Reset errors
    Object.keys(formErrors).forEach(key => {
        formErrors[key as keyof typeof formErrors] = '';
    });
    
    // Validate required fields
    if (!newCustomer.businessName.trim()) {
        formErrors.businessName = 'Business name is required';
        isValid = false;
    }
    
    if (!newCustomer.customerRelationship) {
        formErrors.customerRelationship = 'Customer relationship is required';
        isValid = false;
    }
    
    if (!newCustomer.region) {
        formErrors.region = 'Region is required';
        isValid = false;
    }
    
    if (!newCustomer.customerWebsite.trim()) {
        formErrors.customerWebsite = 'Customer website is required';
        isValid = false;
    } else {
        // Basic URL validation
        try {
            new URL(newCustomer.customerWebsite.startsWith('http') ? newCustomer.customerWebsite : `https://${newCustomer.customerWebsite}`);
        } catch {
            formErrors.customerWebsite = 'Invalid website URL';
            isValid = false;
        }
    }
    
    return isValid;
};

// Add a new customer via API
const addCustomer = async (selectAfterSave = false) => {
    if (validateForm()) {
        try {
            // Build payload based on form inputs and defaults
            const payload: AddCustomerPayload = {
                businessName: newCustomer.businessName.trim(),
                displayName: newCustomer.businessName.trim(),
                legalName: newCustomer.legalName.trim() || newCustomer.businessName.trim(),
                customerCode: '', // backend can generate if not sent
                status: 'Active',
                sfOpportunityId: newCustomer.salesforceOppId.trim(),
                relationshipStatus: newCustomer.customerRelationship as any,
                customerType: 'DSD',
                website: newCustomer.customerWebsite.trim(),
                region: newCustomer.region,
                isGlobalAgreement: newCustomer.isGlobalAgreement as 'Y' | 'N',
                isSubsidiary: newCustomer.isSubsidiary as 'Y' | 'N'
            };

            const createdCustomer: ApiCustomer = await addCustomerApi(payload);

            // Ensure customerId exists (backend might return 'id')
            const normalizedCustomer: ApiCustomer = {
                ...createdCustomer,
                customerId: (createdCustomer as any).customerId ?? (createdCustomer as any).id
            } as ApiCustomer;

            // Push the created customer to the provided customers list (assumed reactive)
            props.customers.push(normalizedCustomer);

            // Select the new customer only if requested
            if (selectAfterSave) {
                emit('update:modelValue', normalizedCustomer);
            }

            // Close dialog
            showDialog.value = false;

            // Reset the form
            Object.assign(newCustomer, {
                businessName: '',
                legalName: '',
                salesforceOppId: '',
                customerRelationship: '',
                customerWebsite: '',
                region: '',
                isGlobalAgreement: 'N',
                isSubsidiary: 'N'
            });
        } catch (e: any) {
            // TODO: show notification; for now set generic form error
            console.error('Failed to add customer', e);
            formErrors.businessName = 'Failed to add customer';
        }
        
    }
};

// Handle customer selection
const handleCustomerChange = (selectedBusinessName: string) => {
    const selectedCustomer = props.customers.find(c => c.businessName === selectedBusinessName);
    emit('update:modelValue', selectedCustomer || null);
    emit('validate');
};

// Filtered customers for autocomplete
const filteredCustomers = computed(() => {
    if (!searchTerm.value) return props.customers;
    
    const search = searchTerm.value.toLowerCase();
    return props.customers.filter(customer => 
        customer.businessName.toLowerCase().includes(search) ||
        customer.legalName.toLowerCase().includes(search)
    );
});

// Get selected customer name
const selectedCustomerName = computed(() => {
    return props.modelValue ? props.modelValue.businessName : '';
});

// Watch for changes in modelValue to update searchTerm
watch(() => props.modelValue, (newValue) => {
    searchTerm.value = newValue ? newValue.businessName : '';
}, { immediate: true });
</script>

<template>
    <div class="d-flex align-center">
        <v-autocomplete
            density="compact"
            :model-value="props.modelValue?.businessName"
            @update:model-value="handleCustomerChange"
            :items="filteredCustomers.map(c => c.businessName)"
            :loading="props.loading"
            :error-messages="error"
            :label="t('page.sales_request_form.customer_details.businessName')"
            class="flex-grow-1"
            v-model:search="searchTerm"
            hide-no-data
            clearable
        >
            <template v-slot:no-data>
                <v-list-item>
                    <v-list-item-title>
                        No customers found. 
                        <v-btn
                            variant="text"
                            color="primary"
                            @click="showDialog = true"
                        >
                            Add new customer
                        </v-btn>
                    </v-list-item-title>
                </v-list-item>
            </template>
        </v-autocomplete>
        
        <v-btn
            icon
            variant="text"
            color="primary"
            class="ml-2 align-self-center"
            style="margin-top: -20px;"
            @click="showDialog = true"
        >
            <v-icon>add</v-icon>
        </v-btn>
        
        <!-- New Customer Dialog -->
        <v-dialog v-model="showDialog" max-width="600px">
            <v-card>
                <v-card-title>
                    <span class="text-h5">Add New Customer</span>
                </v-card-title>
                
                <v-card-text>
                    <v-container>
                        <v-row>
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.businessName"
                                    :error-messages="formErrors.businessName"
                                    label="Business Name *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.legalName"
                                    label="Legal Name"
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.salesforceOppId"
                                    label="Salesforce Opportunity ID"
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-select
                                    v-model="newCustomer.customerRelationship"
                                    :items="customerRelationshipOptionss"
                                    :error-messages="formErrors.customerRelationship"
                                    label="Customer Relationship *"
                                    required
                                    density="compact"
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.customerWebsite"
                                    :error-messages="formErrors.customerWebsite"
                                    label="Customer Website *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>

                            <v-col cols="12">
                                <v-select
                                    v-model="newCustomer.region"
                                    :items="regionOptions"
                                    item-value="value"
                                    item-title="title"
                                    label="Region *"
                                    required
                                    density="compact"
                                ></v-select>
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-select
                                    v-model="newCustomer.isGlobalAgreement"
                                    :items="['Y', 'N']"
                                    label="Global Agreement?"
                                    density="compact"
                                ></v-select>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-select
                                    v-model="newCustomer.isSubsidiary"
                                    :items="['Y', 'N']"
                                    label="Subsidiary?"
                                    density="compact"
                                ></v-select>
                            </v-col>
                        </v-row>
                    </v-container>
                    <small>* indicates required field</small>
                </v-card-text>
                
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="blue-darken-2" variant="plain" @click="showDialog = false">
                        Cancel
                    </v-btn>
                    <v-btn color="blue-darken-1" variant="tonal" @click="addCustomer(false)">
                        Save
                    </v-btn>
                    <v-btn color="blue-darken-1" variant="outlined" @click="addCustomer(true)">
                        Save & Select
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

